Invoke-WebRequest -Uri "http://localhost:5000/api/announcements" -Method GET | Select-Object -ExpandProperty Content | ConvertFrom-Json | ConvertTo-Json -Depth 3

Invoke-WebRequest -Uri "http://localhost:5000/api/menu" -Method GET | Select-Object -ExpandProperty Content | ConvertFrom-Json | ConvertTo-Json -Depth 3

Invoke-WebRequest -Uri "http://localhost:5000/api/menu/featured" -Method GET

Invoke-WebRequest -Uri "http://localhost:5000/api/menu/categories" -Method GET

Invoke-WebRequest -Uri "http://localhost:5000/api/stats/visitors/count" -Method GET

Invoke-WebRequest -Uri "http://localhost:5000/api/stats/visitors/online" -Method GET

Invoke-WebRequest -Uri "http://localhost:5000/api/stats/visitors/count" -Method GET | Select-Object -ExpandProperty Content | ConvertFrom-Json

Invoke-WebRequest -Uri "http://localhost:5000/api/stats/visitors/online" -Method GET | Select-Object -ExpandProperty Content | ConvertFrom-Json

Invoke-RestMethod -Uri "http://localhost:5000/api/gallery" -Method GET

Invoke-RestMethod -Uri "http://localhost:5000/api/gallery/categories" -Method GET

Invoke-RestMethod -Uri "http://localhost:5000/api/auth/csrf-token" -Method GET -SessionVariable session

Invoke-RestMethod -Uri "http://localhost:5000/api/announcements" -Method GET

Invoke-RestMethod -Uri "http://localhost:5000/api/announcements" -Method GET | Select-Object -ExpandProperty announcements | Select-Object title, slug

Invoke-RestMethod -Uri "http://localhost:5000/api/users" -Method GET