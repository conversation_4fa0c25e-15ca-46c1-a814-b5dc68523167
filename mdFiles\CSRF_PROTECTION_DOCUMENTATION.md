# CSRF Protection Documentation

## Overview

Cross-Site Request Forgery (CSRF) protection is a critical security feature implemented in L Café to prevent malicious websites from performing unauthorized actions on behalf of authenticated users. The system uses token-based CSRF protection with cookie storage and header validation.

## Architecture

### Backend Implementation

#### 1. CSRF Middleware (`backend/middleware/csrf.middleware.js`)

The CSRF protection system is built using the `csurf` library with custom wrapper middleware for enhanced logging and error handling.

**Core Configuration:**
```javascript
import csrf from 'csurf';

const csrfMiddleware = csrf({
  cookie: {
    httpOnly: true,        // Prevents XSS attacks
    secure: false,         // Works in both HTTP and HTTPS
    sameSite: 'lax'       // Better compatibility than 'strict'
  }
});
```

**Key Features:**
- **Cookie-based Storage**: CSRF tokens stored in HTTP-only cookies
- **Debug Logging**: Comprehensive logging for troubleshooting
- **Error Handling**: Graceful error responses with detailed messages
- **Development Support**: Enhanced debugging in development mode

#### 2. CSRF Protection Wrapper

```javascript
const csrfProtection = (req, res, next) => {
  // Debug logging
  console.log('CSRF Protection Middleware Called');
  console.log('CSRF Cookie:', req.cookies['_csrf']);
  console.log('CSRF Header:', req.headers['x-csrf-token']);

  csrfMiddleware(req, res, (err) => {
    if (err) {
      if (err.code === 'EBADCSRFTOKEN') {
        return res.status(403).json({
          message: 'Invalid CSRF token. Please refresh the page and try again.',
          error: err.message,
          code: err.code
        });
      }
      return next(err);
    }

    console.log('Generated CSRF Token:', req.csrfToken());
    next();
  });
};
```

#### 3. Error Handler

```javascript
const handleCSRFError = (err, req, res, next) => {
  if (err.code !== 'EBADCSRFTOKEN') {
    return next(err);
  }

  console.log('CSRF Error - Cookies:', req.cookies);
  console.log('CSRF Error - Headers:', req.headers);

  res.status(403).json({
    message: 'Invalid or missing CSRF token. Please refresh the page and try again.',
    error: err.message,
    code: err.code
  });
};
```

### Frontend Implementation

#### 1. CSRF Token Service (`src/services/auth.service.ts`)

**Token Acquisition:**
```typescript
getCsrfToken: async (): Promise<string> => {
  // Check for existing token
  const existingToken = localStorage.getItem('csrfToken');
  if (existingToken) {
    return existingToken;
  }

  // Prevent concurrent requests
  if (csrfTokenPromise) {
    return csrfTokenPromise;
  }

  csrfTokenPromise = new Promise(async (resolve, reject) => {
    try {
      const response = await fetch('http://localhost:5000/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      
      if (!data || !data.csrfToken) {
        throw new Error("Invalid CSRF token response");
      }

      localStorage.setItem('csrfToken', data.csrfToken);
      resolve(data.csrfToken);
    } catch (error) {
      // Fallback in development
      if (process.env.NODE_ENV === 'development') {
        const fallbackToken = 'csrf-fallback-token-' + Date.now();
        localStorage.setItem('csrfToken', fallbackToken);
        resolve(fallbackToken);
      } else {
        reject(error);
      }
    }
  });

  return csrfTokenPromise;
}
```

#### 2. API Integration (`src/services/api.ts`)

**Automatic Token Injection:**
```typescript
api.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    const csrfToken = localStorage.getItem('csrfToken');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }

    return config;
  },
  (error) => Promise.reject(error)
);
```

## Security Features

### 1. Token Generation
- **Cryptographically Secure**: Uses Node.js crypto module
- **Unique Per Session**: Each session gets a unique token
- **Time-Limited**: Tokens expire with session
- **Unpredictable**: High entropy prevents guessing attacks

### 2. Validation Process
1. **Token Presence**: Checks for token in request headers
2. **Token Validity**: Validates against session-stored token
3. **Origin Verification**: Ensures request comes from authorized origin
4. **Double Submit**: Cookie and header must match

### 3. Protection Scope

**Protected Routes:**
- All POST, PUT, DELETE operations
- User authentication endpoints
- Admin panel operations
- Data modification endpoints

**Exempt Routes:**
- GET requests (read-only)
- Public API endpoints
- Static file serving
- Health check endpoints

## Route Integration

### 1. Authentication Routes (`backend/routes/auth.routes.js`)

```javascript
// CSRF token endpoint
router.get('/csrf-token', csrfProtection, (req, res) => {
  res.json({ csrfToken: req.csrfToken() });
});

// Protected login route
router.post('/login', csrfProtection, login);

// Fallback route without CSRF for development
router.post('/direct-login', login);
```

### 2. Gallery Routes (`backend/routes/gallery.routes.js`)

```javascript
// Protected routes with CSRF
router.post('/', [authMiddleware, csrfProtection, galleryUpload], createGalleryItem);
router.put('/:id', [authMiddleware, csrfProtection, galleryUpload], updateGalleryItem);
router.delete('/:id', [authMiddleware, csrfProtection], deleteGalleryItem);
```

### 3. Optional CSRF Protection

For certain routes like visitor tracking, CSRF can be made optional:

```javascript
const optionalCsrf = (req, res, next) => {
  if (process.env.NODE_ENV === 'development' && process.env.SKIP_CSRF === 'true') {
    return next();
  }
  return csrfProtection(req, res, next);
};

router.post('/record', optionalCsrf, recordVisit);
```

## Configuration

### Environment Variables

```env
# CSRF Configuration
NODE_ENV=development|production
SKIP_CSRF=true|false  # Development only

# Cookie Configuration
COOKIE_SECRET=your-secret-key
COOKIE_SECURE=false   # Set to true in production with HTTPS
```

### Application Setup (`backend/app.js`)

```javascript
// Required for CSRF cookies
app.use(cookieParser());

// CORS configuration must allow credentials
const corsOptions = {
  credentials: true,
  origin: function(origin, callback) {
    // Allow specific origins
    callback(null, true);
  }
};

app.use(cors(corsOptions));

// CSRF error handler before routes
app.use(handleCSRFError);
```

## Error Handling

### 1. Common CSRF Errors

**EBADCSRFTOKEN (403)**
- **Cause**: Invalid or missing CSRF token
- **Solution**: Refresh page to get new token
- **Prevention**: Ensure token is included in requests

**Token Mismatch**
- **Cause**: Cookie and header tokens don't match
- **Solution**: Clear localStorage and cookies
- **Prevention**: Proper token synchronization

### 2. Error Response Format

```json
{
  "message": "Invalid CSRF token. Please refresh the page and try again.",
  "error": "invalid csrf token",
  "code": "EBADCSRFTOKEN"
}
```

### 3. Frontend Error Handling

```typescript
try {
  await api.post('/protected-endpoint', data);
} catch (error) {
  if (error.response?.status === 403 && error.response?.data?.code === 'EBADCSRFTOKEN') {
    // Clear token and retry
    localStorage.removeItem('csrfToken');
    const newToken = await authService.getCsrfToken();
    // Retry request with new token
  }
}
```

## Testing

### 1. Manual Testing

**Get CSRF Token:**
```bash
curl -c cookies.txt http://localhost:5000/api/auth/csrf-token
```

**Use Token in Request:**
```bash
curl -b cookies.txt -H "X-CSRF-Token: YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"data":"test"}' \
     http://localhost:5000/api/protected-endpoint
```

### 2. Automated Testing

```javascript
describe('CSRF Protection', () => {
  it('should reject requests without CSRF token', async () => {
    const response = await request(app)
      .post('/api/protected-endpoint')
      .send({ data: 'test' })
      .expect(403);

    expect(response.body.code).toBe('EBADCSRFTOKEN');
  });

  it('should accept requests with valid CSRF token', async () => {
    // Get CSRF token
    const tokenResponse = await request(app)
      .get('/api/auth/csrf-token')
      .expect(200);

    const csrfToken = tokenResponse.body.csrfToken;

    // Use token in protected request
    await request(app)
      .post('/api/protected-endpoint')
      .set('X-CSRF-Token', csrfToken)
      .send({ data: 'test' })
      .expect(200);
  });
});
```

## Best Practices

### 1. Token Management
- **Single Source**: Use one service for token management
- **Caching**: Cache tokens to avoid unnecessary requests
- **Refresh Strategy**: Implement automatic token refresh
- **Cleanup**: Clear tokens on logout

### 2. Error Handling
- **User-Friendly Messages**: Provide clear error messages
- **Automatic Recovery**: Implement retry mechanisms
- **Logging**: Log CSRF failures for monitoring
- **Graceful Degradation**: Handle failures gracefully

### 3. Security Considerations
- **HTTPS Only**: Use secure cookies in production
- **SameSite**: Configure appropriate SameSite policy
- **Token Rotation**: Rotate tokens periodically
- **Monitoring**: Monitor for CSRF attack attempts

## Troubleshooting

### Common Issues

#### 1. Token Not Found
**Symptoms**: 403 errors on all protected requests
**Causes**: 
- Cookies disabled
- CORS misconfiguration
- Missing credentials in requests

**Solutions:**
- Verify `withCredentials: true` in API calls
- Check CORS configuration allows credentials
- Ensure cookies are enabled

#### 2. Token Mismatch
**Symptoms**: Intermittent 403 errors
**Causes:**
- Multiple tabs with different tokens
- Token corruption in localStorage
- Server restart clearing sessions

**Solutions:**
- Implement token synchronization across tabs
- Clear localStorage on errors
- Handle server restarts gracefully

#### 3. Development Issues
**Symptoms**: CSRF blocking development workflow
**Solutions:**
- Use `SKIP_CSRF=true` environment variable
- Implement fallback routes for development
- Use proper development CORS settings

### Debug Commands

```bash
# Check CSRF token endpoint
curl -v http://localhost:5000/api/auth/csrf-token

# Test protected endpoint
curl -v -H "X-CSRF-Token: TOKEN" http://localhost:5000/api/protected

# Check cookies
curl -c cookies.txt -b cookies.txt http://localhost:5000/api/auth/csrf-token
```

## Performance Considerations

### 1. Token Caching
- Cache tokens in localStorage
- Avoid redundant token requests
- Implement token expiration handling

### 2. Request Optimization
- Include tokens in all requests upfront
- Batch token refresh operations
- Minimize token validation overhead

### 3. Monitoring
- Track CSRF token generation rates
- Monitor failed validation attempts
- Alert on unusual CSRF patterns

## Conclusion

The CSRF protection system in L Café provides robust security against cross-site request forgery attacks while maintaining usability and performance. The implementation includes comprehensive error handling, debugging capabilities, and flexible configuration options for different environments.

Key strengths:
- **Comprehensive Protection**: Covers all state-changing operations
- **User-Friendly**: Graceful error handling and recovery
- **Developer-Friendly**: Extensive logging and debugging features
- **Flexible**: Configurable for different environments
- **Secure**: Industry-standard protection mechanisms
