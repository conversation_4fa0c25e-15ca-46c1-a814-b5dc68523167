# Gallery Feature Documentation

## Overview

The Gallery feature is a comprehensive image management system for L Café that allows administrators to upload, organize, and display images across different categories. The system includes both public viewing capabilities and administrative management tools with automatic image optimization and thumbnail generation.

## Architecture

### Backend Components

#### 1. Database Model (`backend/models/gallery.model.js`)

The Gallery system uses a MongoDB schema with the following structure:

```javascript
const galleryItemSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  category: { 
    type: String, 
    enum: ['interior', 'food', 'drinks', 'events', 'team'],
    default: 'interior'
  },
  imagePath: { type: String, required: true },
  thumbnail: { type: String },
  featured: { type: Boolean, default: false },
  sortOrder: { type: Number, default: 0 },
  uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Key Features:**
- **Categorization**: Predefined categories for organizing images
- **Featured System**: Mark important images for special display
- **Soft Delete**: Uses `active` field for non-destructive deletion
- **User Tracking**: Links images to the user who uploaded them
- **Sorting**: Custom sort order for display control

#### 2. API Routes (`backend/routes/gallery.routes.js`)

**Public Routes (No Authentication Required):**
- `GET /api/gallery` - Get all gallery items with pagination
- `GET /api/gallery/categories` - Get available categories
- `GET /api/gallery/category/:category` - Get items by category
- `GET /api/gallery/featured` - Get featured items
- `GET /api/gallery/:id` - Get single item by ID

**Protected Routes (Authentication Required):**
- `POST /api/gallery` - Create new gallery item (with image upload)
- `PUT /api/gallery/:id` - Update gallery item
- `DELETE /api/gallery/:id` - Soft delete gallery item

**Admin-Only Routes:**
- `DELETE /api/gallery/:id/permanent` - Permanently delete item and files

#### 3. Controller Logic (`backend/controllers/gallery.controller.js`)

**Complete Controller Implementation:**

```javascript
// controllers/gallery.controller.js
import GalleryItem from '../models/gallery.model.js';
import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';

// Get all gallery items with pagination and filtering
export const getGalleryItems = async (req, res) => {
  try {
    const { page = 1, limit = 10, category, featured, active = 'true' } = req.query;

    // Build filter object
    const filter = { active: active === 'true' };

    if (category) filter.category = category;
    if (featured !== undefined) filter.featured = featured === 'true';

    // Calculate how many documents to skip
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get gallery items with pagination
    const galleryItems = await GalleryItem.find(filter)
      .sort({ sortOrder: 1, createdAt: -1 }) // Sort by sortOrder, then by date
      .skip(skip)
      .limit(parseInt(limit))
      .populate('uploadedBy', 'username firstName lastName');

    // Get total count for pagination
    const total = await GalleryItem.countDocuments(filter);

    res.status(200).json({
      galleryItems,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching gallery items',
      error: error.message
    });
  }
};

// Get gallery categories
export const getGalleryCategories = async (req, res) => {
  try {
    const categories = await GalleryItem.distinct('category');
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching gallery categories',
      error: error.message
    });
  }
};

// Get gallery items by category
export const getGalleryItemsByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const galleryItems = await GalleryItem.find({
      category,
      active: true
    })
      .sort({ sortOrder: 1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('uploadedBy', 'username firstName lastName');

    const total = await GalleryItem.countDocuments({ category, active: true });

    res.status(200).json({
      galleryItems,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching gallery items by category',
      error: error.message
    });
  }
};

// Get featured gallery items
export const getFeaturedGalleryItems = async (req, res) => {
  try {
    const featuredItems = await GalleryItem.find({
      featured: true,
      active: true
    })
      .sort({ sortOrder: 1, createdAt: -1 })
      .limit(10)
      .populate('uploadedBy', 'username firstName lastName');

    res.status(200).json(featuredItems);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching featured gallery items',
      error: error.message
    });
  }
};

// Get single gallery item by ID
export const getGalleryItemById = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid gallery item ID' });
    }

    const galleryItem = await GalleryItem.findOne({ _id: id, active: true })
      .populate('uploadedBy', 'username firstName lastName');

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    res.status(200).json(galleryItem);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching gallery item',
      error: error.message
    });
  }
};

// Create a new gallery item
export const createGalleryItem = async (req, res) => {
  try {
    const { title, description, category, featured, sortOrder } = req.body;

    // Validation
    if (!title) {
      return res.status(400).json({ message: 'Title is required' });
    }

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ message: 'Image file is required' });
    }

    // Get image path
    const imagePath = `/uploads/gallery/${req.file.filename}`;

    // Create thumbnail path if available
    let thumbnail = null;
    if (req.thumbnail) {
      thumbnail = `/uploads/gallery/thumbnails/${req.thumbnail.filename}`;
    }

    // Create new gallery item
    const galleryItem = new GalleryItem({
      title,
      description,
      category,
      imagePath,
      thumbnail,
      featured: featured === 'true',
      sortOrder: sortOrder ? parseInt(sortOrder) : 0,
      uploadedBy: req.user.id, // From auth middleware
      active: true
    });

    // Save to database
    await galleryItem.save();

    res.status(201).json({
      message: 'Gallery item created successfully',
      galleryItem
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error creating gallery item',
      error: error.message
    });
  }
};

// Update an existing gallery item
export const updateGalleryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, category, featured, sortOrder } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid gallery item ID' });
    }

    const galleryItem = await GalleryItem.findById(id);

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    // Update fields
    const updateData = {
      title: title || galleryItem.title,
      description: description !== undefined ? description : galleryItem.description,
      category: category || galleryItem.category,
      featured: featured !== undefined ? featured === 'true' : galleryItem.featured,
      sortOrder: sortOrder !== undefined ? parseInt(sortOrder) : galleryItem.sortOrder,
      updatedAt: Date.now()
    };

    // Handle new image upload
    if (req.file) {
      // Delete old files
      if (galleryItem.imagePath) {
        const oldImagePath = path.join(__dirname, '..', 'public', galleryItem.imagePath);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }

      if (galleryItem.thumbnail) {
        const oldThumbnailPath = path.join(__dirname, '..', 'public', galleryItem.thumbnail);
        if (fs.existsSync(oldThumbnailPath)) {
          fs.unlinkSync(oldThumbnailPath);
        }
      }

      // Set new image paths
      updateData.imagePath = `/uploads/gallery/${req.file.filename}`;
      updateData.thumbnail = req.thumbnail ? `/uploads/gallery/thumbnails/${req.thumbnail.filename}` : null;
    }

    const updatedGalleryItem = await GalleryItem.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('uploadedBy', 'username firstName lastName');

    res.status(200).json({
      message: 'Gallery item updated successfully',
      galleryItem: updatedGalleryItem
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error updating gallery item',
      error: error.message
    });
  }
};

// Delete a gallery item (soft delete)
export const deleteGalleryItem = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid gallery item ID' });
    }

    const galleryItem = await GalleryItem.findByIdAndUpdate(
      id,
      { active: false, updatedAt: Date.now() },
      { new: true }
    );

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    res.status(200).json({
      message: 'Gallery item deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error deleting gallery item',
      error: error.message
    });
  }
};

// Hard delete a gallery item (admin only)
export const hardDeleteGalleryItem = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid gallery item ID' });
    }

    const galleryItem = await GalleryItem.findById(id);

    if (!galleryItem) {
      return res.status(404).json({ message: 'Gallery item not found' });
    }

    // Delete files from filesystem
    if (galleryItem.imagePath) {
      const imagePath = path.join(__dirname, '..', 'public', galleryItem.imagePath);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    if (galleryItem.thumbnail) {
      const thumbnailPath = path.join(__dirname, '..', 'public', galleryItem.thumbnail);
      if (fs.existsSync(thumbnailPath)) {
        fs.unlinkSync(thumbnailPath);
      }
    }

    // Delete from database
    await GalleryItem.findByIdAndDelete(id);

    res.status(200).json({
      message: 'Gallery item permanently deleted'
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error permanently deleting gallery item',
      error: error.message
    });
  }
};
```

#### 4. Upload Middleware (`backend/middleware/upload.middleware.js`)

**Features:**
- **File Validation**: Only accepts image files
- **Size Limits**: 5MB maximum file size
- **Unique Naming**: Timestamp-based filename generation
- **Automatic Thumbnails**: Creates 300x200 thumbnails using Sharp
- **Directory Management**: Auto-creates required directories

**File Structure:**
```
backend/public/uploads/gallery/
├── image-[timestamp]-[random].ext     # Original images
└── thumbnails/
    └── thumbnail-image-[timestamp]-[random].ext  # Generated thumbnails
```

### Frontend Components

#### 1. Public Gallery Page (`src/pages/Gallery.tsx`)

**Features:**
- **Category Tabs**: Filter by category or view all/featured
- **Responsive Grid**: Adaptive layout for different screen sizes
- **Image Modal**: Click to view full-size images with details
- **Loading States**: Smooth loading indicators
- **Error Handling**: Graceful error display

**Key Functions:**
- `getImageUrl()`: Constructs proper image URLs from backend paths
- Category-based data fetching with React Query
- Modal state management for image viewing

#### 2. Admin Dashboard (`src/components/dashboard/DashboardGallery.tsx`)

**Management Features:**
- **Grid View**: Visual overview of all gallery items
- **Quick Actions**: Edit, delete, and feature toggle
- **Status Indicators**: Featured badges and category labels
- **Pagination**: Handle large image collections
- **Add New**: Quick access to upload new images

#### 3. Gallery Item Form (`src/components/dashboard/GalleryItemForm.tsx`)

**Form Features:**
- **Image Upload**: Drag-and-drop or click to upload
- **Live Preview**: Shows selected image before upload
- **Category Selection**: Dropdown with predefined categories
- **Featured Toggle**: Mark items as featured
- **Sort Order**: Custom ordering for display
- **Validation**: Client-side form validation with Zod

**Form Schema:**
```typescript
const galleryItemSchema = z.object({
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string().optional(),
  category: z.string().min(1, "Please select a category"),
  featured: z.boolean().default(false),
  sortOrder: z.coerce.number().int().default(0),
});
```

#### 4. Service Layer (`src/services/gallery.service.ts`)

**API Integration:**
- **CRUD Operations**: Full create, read, update, delete functionality
- **CSRF Protection**: Automatic CSRF token handling
- **File Upload**: Multipart form data handling
- **Error Handling**: Comprehensive error catching and logging

### Security Features

#### 1. Authentication & Authorization
- **JWT Authentication**: Required for all write operations
- **Role-Based Access**: Admin-only routes for sensitive operations
- **CSRF Protection**: Prevents cross-site request forgery attacks

#### 2. File Security
- **File Type Validation**: Only image files accepted
- **Size Limits**: Prevents large file uploads
- **Secure Storage**: Files stored outside web root
- **Path Validation**: Prevents directory traversal attacks

#### 3. Data Validation
- **Input Sanitization**: All inputs validated and sanitized
- **MongoDB Injection Prevention**: Mongoose provides protection
- **XSS Prevention**: React's built-in XSS protection

### Image Processing

#### 1. Upload Process
1. **File Validation**: Check type and size
2. **Unique Naming**: Generate timestamp-based filename
3. **Storage**: Save to gallery directory
4. **Thumbnail Generation**: Create 300x200 thumbnail using Sharp
5. **Database Record**: Create entry with file paths

#### 2. Serving Images
- **Static File Serving**: Express serves from public directory
- **CORS Headers**: Proper headers for cross-origin access
- **Proxy Configuration**: Vite proxies uploads to backend
- **Caching**: Browser caching for performance

### Performance Optimizations

#### 1. Database
- **Indexing**: Indexes on frequently queried fields
- **Pagination**: Prevents large data transfers
- **Selective Population**: Only populate needed user fields
- **Efficient Queries**: Optimized MongoDB queries

#### 2. Frontend
- **React Query**: Caching and background updates
- **Lazy Loading**: Images load as needed
- **Thumbnail Usage**: Smaller images for grid views
- **Code Splitting**: Component-level code splitting

#### 3. Image Optimization
- **Automatic Thumbnails**: Faster loading for grid views
- **Sharp Processing**: High-performance image processing
- **Responsive Images**: Different sizes for different contexts
- **Format Optimization**: Maintains quality while reducing size

## API Reference

### Public Endpoints

#### Get Gallery Items
```http
GET /api/gallery?page=1&limit=10&category=interior&featured=true
```

**Query Parameters:**
- `page` (number): Page number for pagination
- `limit` (number): Items per page
- `category` (string): Filter by category
- `featured` (boolean): Filter featured items
- `active` (boolean): Filter active items (default: true)

**Response:**
```json
{
  "galleryItems": [...],
  "totalPages": 5,
  "currentPage": 1,
  "total": 50
}
```

#### Get Categories
```http
GET /api/gallery/categories
```

**Response:**
```json
["interior", "food", "drinks", "events", "team"]
```

#### Get Featured Items
```http
GET /api/gallery/featured
```

**Response:**
```json
[
  {
    "_id": "...",
    "title": "Featured Image",
    "description": "...",
    "category": "interior",
    "imagePath": "/uploads/gallery/image-123.jpg",
    "thumbnail": "/uploads/gallery/thumbnails/thumbnail-image-123.jpg",
    "featured": true,
    "sortOrder": 1,
    "uploadedBy": {
      "_id": "...",
      "username": "admin",
      "firstName": "Admin",
      "lastName": "User"
    },
    "active": true,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  }
]
```

### Protected Endpoints

#### Create Gallery Item
```http
POST /api/gallery
Content-Type: multipart/form-data
Authorization: Bearer <token>
X-CSRF-Token: <csrf-token>
```

**Form Data:**
- `image` (file): Image file to upload
- `title` (string): Image title
- `description` (string, optional): Image description
- `category` (string): Image category
- `featured` (boolean): Whether item is featured
- `sortOrder` (number): Display order

#### Update Gallery Item
```http
PUT /api/gallery/:id
Content-Type: multipart/form-data
Authorization: Bearer <token>
X-CSRF-Token: <csrf-token>
```

#### Delete Gallery Item
```http
DELETE /api/gallery/:id
Authorization: Bearer <token>
X-CSRF-Token: <csrf-token>
```

## Configuration

### Environment Variables

```env
# File upload limits
MAX_FILE_SIZE=5242880  # 5MB in bytes

# Public URL for generating absolute URLs
PUBLIC_URL=http://localhost:5000

# MongoDB connection
MONGODB_URI=mongodb://localhost:27017/l-cafe
```

### Vite Configuration

The frontend requires proxy configuration for image serving:

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/uploads': {
        target: 'http://localhost:5000',
        changeOrigin: true,
      },
    },
  },
});
```

## File Structure

### Backend Files
```
backend/
├── models/
│   └── gallery.model.js              # MongoDB schema
├── controllers/
│   └── gallery.controller.js         # Business logic
├── routes/
│   └── gallery.routes.js             # Route definitions
├── middleware/
│   └── upload.middleware.js          # File upload handling
└── public/
    └── uploads/
        └── gallery/
            ├── *.jpg, *.png          # Original images
            └── thumbnails/
                └── thumbnail-*.jpg    # Generated thumbnails
```

### Frontend Files
```
src/
├── pages/
│   └── Gallery.tsx                   # Public gallery page
├── components/
│   └── dashboard/
│       ├── DashboardGallery.tsx      # Admin gallery management
│       ├── GalleryItemForm.tsx       # Add/edit form
│       └── DeleteGalleryItemDialog.tsx # Delete confirmation
└── services/
    └── gallery.service.ts            # API service layer
```

## Usage Examples

### Adding a New Gallery Item

1. **Navigate to Admin Dashboard**
   ```
   http://localhost:8080/dashboard/gallery
   ```

2. **Click "Add Image" Button**

3. **Fill Form:**
   - Upload image file
   - Enter title and description
   - Select category
   - Set featured status
   - Assign sort order

4. **Submit Form**
   - Image is uploaded and processed
   - Thumbnail is automatically generated
   - Database record is created

### Viewing Gallery

1. **Public Gallery Page**
   ```
   http://localhost:8080/gallery
   ```

2. **Filter by Category**
   - Click category tabs to filter
   - View "Featured" items
   - Browse "All" items

3. **View Full Image**
   - Click any thumbnail
   - Modal opens with full-size image
   - View image details and metadata

## Troubleshooting

### Common Issues

#### Images Not Loading
- **Check Proxy Configuration**: Ensure Vite proxy is configured for `/uploads`
- **Verify File Paths**: Check that image paths in database match filesystem
- **CORS Issues**: Verify CORS headers are set correctly

#### Upload Failures
- **File Size**: Check if file exceeds 5MB limit
- **File Type**: Ensure only image files are uploaded
- **Permissions**: Verify write permissions on uploads directory
- **CSRF Token**: Ensure CSRF token is valid

#### Database Issues
- **Connection**: Verify MongoDB connection
- **Schema Validation**: Check that data matches schema requirements
- **Indexes**: Ensure proper indexes for performance

### Debug Commands

```bash
# Test API endpoints
curl http://localhost:5000/api/gallery
curl http://localhost:5000/api/gallery/categories
curl http://localhost:5000/api/gallery/featured

# Check file permissions
ls -la backend/public/uploads/gallery/

# Verify database records
mongo l-cafe --eval "db.galleryitems.find().pretty()"
```

## Future Enhancements

### Planned Features
- **Bulk Upload**: Multiple file upload capability
- **Image Editing**: Basic editing tools (crop, resize, filters)
- **Advanced Search**: Search by title, description, tags
- **Image Metadata**: EXIF data extraction and display
- **CDN Integration**: Cloud storage and CDN support
- **Image Compression**: Advanced compression algorithms
- **Watermarking**: Automatic watermark application

### Performance Improvements
- **Lazy Loading**: Progressive image loading
- **WebP Support**: Modern image format support
- **Image Variants**: Multiple sizes for different use cases
- **Caching Strategy**: Advanced caching mechanisms

## Code Examples

### Backend Implementation Examples

#### Gallery Model with Hooks
```javascript
// models/gallery.model.js
import mongoose from 'mongoose';

const galleryItemSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  category: {
    type: String,
    enum: ['interior', 'food', 'drinks', 'events', 'team'],
    default: 'interior'
  },
  imagePath: { type: String, required: true },
  thumbnail: { type: String },
  featured: { type: Boolean, default: false },
  sortOrder: { type: Number, default: 0 },
  uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Pre-save hook to update timestamp
galleryItemSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for performance
galleryItemSchema.index({ category: 1, active: 1 });
galleryItemSchema.index({ featured: 1, active: 1 });
galleryItemSchema.index({ sortOrder: 1, createdAt: -1 });

export default mongoose.model('GalleryItem', galleryItemSchema);
```

#### Upload Middleware Implementation
```javascript
// middleware/upload.middleware.js
import multer from 'multer';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs';

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const galleryDir = path.join(__dirname, '..', 'public', 'uploads', 'gallery');
    cb(null, galleryDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

export const galleryUpload = (req, res, next) => {
  upload.single('image')(req, res, async function (err) {
    if (err) {
      return res.status(400).json({ message: `Upload error: ${err.message}` });
    }

    if (!req.file) return next();

    try {
      // Create thumbnail
      const thumbnailFilename = `thumbnail-${req.file.filename}`;
      const thumbnailPath = path.join(thumbnailsDir, thumbnailFilename);

      await sharp(req.file.path)
        .resize(300, 200, { fit: 'cover' })
        .toFile(thumbnailPath);

      req.thumbnail = {
        filename: thumbnailFilename,
        path: thumbnailPath
      };

      next();
    } catch (error) {
      return res.status(500).json({
        message: `Thumbnail creation error: ${error.message}`
      });
    }
  });
};
```

#### Controller with Error Handling
```javascript
// controllers/gallery.controller.js
export const createGalleryItem = async (req, res) => {
  try {
    const { title, description, category, featured, sortOrder } = req.body;

    // Validation
    if (!title) {
      return res.status(400).json({ message: 'Title is required' });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'Image file is required' });
    }

    // Create gallery item
    const galleryItem = new GalleryItem({
      title,
      description,
      category,
      imagePath: `/uploads/gallery/${req.file.filename}`,
      thumbnail: req.thumbnail ? `/uploads/gallery/thumbnails/${req.thumbnail.filename}` : null,
      featured: featured === 'true',
      sortOrder: sortOrder ? parseInt(sortOrder) : 0,
      uploadedBy: req.user.id,
      active: true
    });

    await galleryItem.save();

    res.status(201).json({
      message: 'Gallery item created successfully',
      galleryItem
    });
  } catch (error) {
    // Clean up uploaded files on error
    if (req.file) {
      fs.unlink(req.file.path, () => {});
    }
    if (req.thumbnail) {
      fs.unlink(req.thumbnail.path, () => {});
    }

    res.status(500).json({
      message: 'Error creating gallery item',
      error: error.message
    });
  }
};
```

### Frontend Implementation Examples

#### React Query Integration
```typescript
// services/gallery.service.ts
import api from './api';
import authService from './auth.service';

const galleryService = {
  getGalleryItems: async (page = 1, limit = 10) => {
    return api.get(`/gallery?page=${page}&limit=${limit}`);
  },

  createGalleryItem: async (formData: FormData) => {
    try {
      await authService.getCsrfToken();
      return api.post('/gallery', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } catch (error) {
      console.error('Error in createGalleryItem service:', error);
      throw error;
    }
  },

  updateGalleryItem: async (id: string, formData: FormData) => {
    try {
      await authService.getCsrfToken();
      return api.put(`/gallery/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } catch (error) {
      console.error('Error in updateGalleryItem service:', error);
      throw error;
    }
  }
};

export default galleryService;
```

#### Gallery Component with Hooks
```typescript
// pages/Gallery.tsx
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import galleryService, { GalleryItem } from '@/services/gallery.service';

const Gallery = () => {
  const [activeCategory, setActiveCategory] = useState("all");
  const [selectedImage, setSelectedImage] = useState<GalleryItem | null>(null);

  const { data: galleryItems, isLoading } = useQuery({
    queryKey: ['gallery-items', activeCategory],
    queryFn: async () => {
      if (activeCategory === "all") {
        const response = await galleryService.getGalleryItems(1, 50);
        return response.data.galleryItems;
      } else if (activeCategory === "featured") {
        const response = await galleryService.getFeaturedGalleryItems();
        return response.data;
      } else {
        const response = await galleryService.getGalleryItemsByCategory(activeCategory, 1, 50);
        return response.data.galleryItems;
      }
    },
  });

  const getImageUrl = (path: string) => {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    const baseUrl = api.defaults.baseURL?.replace('/api', '') || '';
    return `${baseUrl}${path}`;
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Gallery grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {galleryItems?.map((item: GalleryItem) => (
          <div
            key={item._id}
            className="cursor-pointer group overflow-hidden rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300"
            onClick={() => setSelectedImage(item)}
          >
            <img
              src={getImageUrl(item.thumbnail || item.imagePath)}
              alt={item.title}
              className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
            />
          </div>
        ))}
      </div>
    </div>
  );
};
```

#### Form with Validation
```typescript
// components/dashboard/GalleryItemForm.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const galleryItemSchema = z.object({
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string().optional(),
  category: z.string().min(1, "Please select a category"),
  featured: z.boolean().default(false),
  sortOrder: z.coerce.number().int().default(0),
});

type GalleryFormValues = z.infer<typeof galleryItemSchema>;

const GalleryItemForm = ({ galleryItem, onSuccess, onCancel }) => {
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");

  const form = useForm<GalleryFormValues>({
    resolver: zodResolver(galleryItemSchema),
    defaultValues: {
      title: galleryItem?.title || "",
      description: galleryItem?.description || "",
      category: galleryItem?.category || "",
      featured: galleryItem?.featured || false,
      sortOrder: galleryItem?.sortOrder || 0,
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      const reader = new FileReader();
      reader.onload = () => setPreviewUrl(reader.result as string);
      reader.readAsDataURL(selectedFile);
    }
  };

  const onSubmit = async (data: GalleryFormValues) => {
    try {
      const formData = new FormData();

      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      if (file) {
        formData.append('image', file);
      }

      if (galleryItem) {
        await galleryService.updateGalleryItem(galleryItem._id, formData);
      } else {
        await galleryService.createGalleryItem(formData);
      }

      onSuccess();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Form fields */}
      </form>
    </Form>
  );
};
```

## Testing

### Unit Tests Example

```javascript
// tests/gallery.test.js
import request from 'supertest';
import app from '../app.js';
import GalleryItem from '../models/gallery.model.js';

describe('Gallery API', () => {
  beforeEach(async () => {
    await GalleryItem.deleteMany({});
  });

  describe('GET /api/gallery', () => {
    it('should return empty gallery items', async () => {
      const response = await request(app)
        .get('/api/gallery')
        .expect(200);

      expect(response.body.galleryItems).toHaveLength(0);
      expect(response.body.total).toBe(0);
    });

    it('should return gallery items with pagination', async () => {
      // Create test data
      await GalleryItem.create({
        title: 'Test Image',
        imagePath: '/test/path.jpg',
        category: 'interior'
      });

      const response = await request(app)
        .get('/api/gallery?page=1&limit=10')
        .expect(200);

      expect(response.body.galleryItems).toHaveLength(1);
      expect(response.body.currentPage).toBe(1);
    });
  });

  describe('POST /api/gallery', () => {
    it('should create gallery item with authentication', async () => {
      const token = 'valid-jwt-token';

      const response = await request(app)
        .post('/api/gallery')
        .set('Authorization', `Bearer ${token}`)
        .field('title', 'Test Image')
        .field('category', 'interior')
        .attach('image', 'tests/fixtures/test-image.jpg')
        .expect(201);

      expect(response.body.message).toBe('Gallery item created successfully');
    });
  });
});
```

### Integration Tests

```javascript
// tests/gallery.integration.test.js
describe('Gallery Feature Integration', () => {
  it('should handle complete gallery workflow', async () => {
    // 1. Upload image
    const uploadResponse = await request(app)
      .post('/api/gallery')
      .set('Authorization', `Bearer ${adminToken}`)
      .field('title', 'Integration Test Image')
      .field('category', 'interior')
      .field('featured', 'true')
      .attach('image', testImagePath)
      .expect(201);

    const galleryItemId = uploadResponse.body.galleryItem._id;

    // 2. Verify image appears in gallery
    const galleryResponse = await request(app)
      .get('/api/gallery')
      .expect(200);

    expect(galleryResponse.body.galleryItems).toHaveLength(1);

    // 3. Verify featured items
    const featuredResponse = await request(app)
      .get('/api/gallery/featured')
      .expect(200);

    expect(featuredResponse.body).toHaveLength(1);

    // 4. Update gallery item
    await request(app)
      .put(`/api/gallery/${galleryItemId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .field('title', 'Updated Title')
      .expect(200);

    // 5. Delete gallery item
    await request(app)
      .delete(`/api/gallery/${galleryItemId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .expect(200);
  });
});
```

## Deployment Considerations

### Production Configuration

```javascript
// Production environment variables
NODE_ENV=production
MONGODB_URI=mongodb://production-server:27017/l-cafe
MAX_FILE_SIZE=10485760  // 10MB for production
PUBLIC_URL=https://your-domain.com
CORS_ORIGIN=https://your-frontend-domain.com
```

### Nginx Configuration

```nginx
# nginx.conf
server {
    listen 80;
    server_name your-domain.com;

    # Serve static files directly
    location /uploads/ {
        alias /path/to/backend/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Proxy API requests
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Serve frontend
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
}
```

### Docker Configuration

```dockerfile
# Dockerfile for backend
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

# Create uploads directory
RUN mkdir -p public/uploads/gallery/thumbnails

EXPOSE 5000

CMD ["npm", "start"]
```

## Monitoring and Logging

### Error Tracking

```javascript
// Enhanced error logging
export const createGalleryItem = async (req, res) => {
  try {
    // ... implementation
  } catch (error) {
    // Log error details
    console.error('Gallery creation error:', {
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
      fileName: req.file?.filename,
      timestamp: new Date().toISOString()
    });

    // Clean up files
    if (req.file) fs.unlink(req.file.path, () => {});
    if (req.thumbnail) fs.unlink(req.thumbnail.path, () => {});

    res.status(500).json({
      message: 'Error creating gallery item',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};
```

### Performance Monitoring

```javascript
// Performance metrics
const performanceMiddleware = (req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);

    // Log slow requests
    if (duration > 1000) {
      console.warn(`Slow request detected: ${req.method} ${req.path} took ${duration}ms`);
    }
  });

  next();
};
```

## Conclusion

The Gallery feature provides a robust, secure, and user-friendly image management system for L Café. It combines powerful backend processing with an intuitive frontend interface, ensuring both administrators and visitors have an excellent experience with the café's visual content.

The system is designed for scalability, security, and performance, with comprehensive error handling and validation throughout. The modular architecture allows for easy maintenance and future enhancements while maintaining backward compatibility.

Key strengths of the implementation:
- **Comprehensive Security**: Multi-layered security with authentication, authorization, and input validation
- **Performance Optimized**: Efficient database queries, image optimization, and caching strategies
- **User-Friendly**: Intuitive interfaces for both administrators and public users
- **Maintainable Code**: Clean architecture with separation of concerns
- **Robust Error Handling**: Graceful error handling and recovery mechanisms
- **Scalable Design**: Built to handle growth in content and user base
