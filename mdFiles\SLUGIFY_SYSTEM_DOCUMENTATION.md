# Slugify System Documentation

## Overview

The Slugify system in L Café automatically generates URL-friendly identifiers (slugs) from titles and names across various content types. This system ensures SEO-friendly URLs, prevents conflicts, and maintains consistency across the application while supporting internationalization.

## Architecture

### Core Implementation

#### 1. Slugify Library Configuration

The system uses the `slugify` npm package with consistent configuration across all models:

```javascript
import slugify from 'slugify';

const generateSlug = (text) => {
  return slugify(text, {
    lower: true,      // Convert to lowercase
    strict: true,     // Remove special characters
    locale: 'tr'      // Support Turkish characters
  });
};
```

**Configuration Options:**
- **`lower: true`**: Converts all characters to lowercase
- **`strict: true`**: Removes special characters and symbols
- **`locale: 'tr'`**: Handles Turkish characters (ç, ğ, ı, ö, ş, ü)

#### 2. Unique Slug Generation

Each model implements a unique slug generation strategy to prevent conflicts:

```javascript
const createUniqueSlug = async (title, Model, excludeId = null) => {
  let slug = slugify(title, {
    lower: true,
    strict: true,
    locale: 'tr'
  });
  
  let exists = await Model.findOne({ 
    slug, 
    ...(excludeId && { _id: { $ne: excludeId } })
  });
  
  if (exists) {
    let count = 1;
    let newSlug = slug;
    
    while (exists) {
      newSlug = `${slug}-${count}`;
      exists = await Model.findOne({ 
        slug: newSlug,
        ...(excludeId && { _id: { $ne: excludeId } })
      });
      count++;
    }
    
    slug = newSlug;
  }
  
  return slug;
};
```

## Model Implementations

### 1. Announcement Model (`backend/models/announcement.model.js`)

**Schema Definition:**
```javascript
const announcementSchema = new mongoose.Schema({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  // ... other fields
});
```

**Controller Implementation:**
```javascript
// In announcement.controller.js
const createUniqueSlug = async (title) => {
  let slug = slugify(title, {
    lower: true,
    strict: true,
    locale: 'tr'
  });
  
  let exists = await Announcement.findOne({ slug });
  
  if (exists) {
    let count = 1;
    let newSlug = slug;
    
    while (exists) {
      newSlug = `${slug}-${count}`;
      exists = await Announcement.findOne({ slug: newSlug });
      count++;
    }
    
    slug = newSlug;
  }
  
  return slug;
};

export const createAnnouncement = async (req, res) => {
  const { title, content, summary, category } = req.body;
  
  // Generate unique slug
  const slug = await createUniqueSlug(title);
  
  const announcement = new Announcement({
    title,
    slug,
    content,
    summary,
    category,
    author: req.user.id,
    active: true
  });
  
  await announcement.save();
  // ...
};
```

### 2. Menu Item Model (`backend/models/menu.model.js`)

**Pre-Save Hook Implementation:**
```javascript
menuItemSchema.pre('save', async function(next) {
  if (this.isModified('name')) {
    let slug = slugify(this.name, {
      lower: true,
      strict: true,
      locale: 'tr'
    });
    
    const slugExists = await this.constructor.findOne({ 
      slug, 
      _id: { $ne: this._id } 
    });
    
    if (slugExists) {
      slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
    }
    
    this.slug = slug;
  }
  
  this.updatedAt = Date.now();
  next();
});
```

**Controller Integration:**
```javascript
export const createMenuItem = async (req, res) => {
  const { name, description, category, price } = req.body;
  
  // Create slug from name
  let slug = slugify(name, {
    lower: true,
    strict: true,
    locale: 'tr'
  });
  
  // Check if slug exists
  const slugExists = await MenuItem.findOne({ slug });
  
  if (slugExists) {
    slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
  }
  
  const menuItem = new MenuItem({
    name,
    slug,
    description,
    category,
    price: parseFloat(price),
    active: true
  });
  
  await menuItem.save();
  // ...
};
```

### 3. Q&A Model (`backend/models/qa.model.js`)

**Schema Definition:**
```javascript
const qaSchema = new mongoose.Schema({
  question: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  answer: { type: String, default: '' },
  // ... other fields
});
```

**Controller Implementation:**
```javascript
// In qa.controller.js
const createUniqueSlug = async (question) => {
  let slug = slugify(question, {
    lower: true,
    strict: true,
    locale: 'tr'
  });

  let exists = await QA.findOne({ slug });

  if (exists) {
    let count = 1;
    let newSlug = slug;

    while (exists) {
      newSlug = `${slug}-${count}`;
      exists = await QA.findOne({ slug: newSlug });
      count++;
    }

    slug = newSlug;
  }

  return slug;
};

export const createQAItem = async (req, res) => {
  const { question, answer, category } = req.body;
  
  const slug = await createUniqueSlug(question);
  
  const qaItem = new QA({
    question,
    slug,
    answer,
    category,
    approved: true,
    active: true
  });
  
  await qaItem.save();
  // ...
};
```

## Slug Generation Examples

### Input/Output Examples

```javascript
// Basic examples
"Hello World" → "hello-world"
"Café Latte Special" → "cafe-latte-special"
"Turkish Coffee & Delight" → "turkish-coffee-and-delight"

// Special characters
"What is your opening hours?" → "what-is-your-opening-hours"
"Gluten-Free Options Available" → "gluten-free-options-available"
"Special Characters: @#$%^&*()" → "special-characters-dollarpercentand"

// Turkish characters
"Çay ve Kahve Menüsü" → "cay-ve-kahve-menusu"
"İstanbul Kahvesi" → "istanbul-kahvesi"
"Güzel Manzara" → "guzel-manzara"

// Multiple spaces and case
"Multiple    Spaces   Test" → "multiple-spaces-test"
"UPPERCASE TITLE" → "uppercase-title"
"mixed CaSe TiTlE" → "mixed-case-title"

// Conflict resolution
"Coffee Menu" → "coffee-menu"
"Coffee Menu" (duplicate) → "coffee-menu-1"
"Coffee Menu" (another) → "coffee-menu-2"
```

## URL Integration

### 1. Frontend Routing

Slugs are used to create SEO-friendly URLs:

```typescript
// Route definitions
const routes = [
  { path: '/announcements/:slug', component: AnnouncementDetail },
  { path: '/menu/:slug', component: MenuItemDetail },
  { path: '/faq/:slug', component: QADetail }
];

// URL examples
/announcements/summer-promotion-2024
/menu/turkish-coffee-special
/faq/what-are-your-opening-hours
```

### 2. API Endpoints

```javascript
// Get by slug endpoints
router.get('/announcements/:slug', getAnnouncementBySlug);
router.get('/menu/:slug', getMenuItemBySlug);
router.get('/qa/:slug', getQAItemBySlug);

// Controller implementation
export const getAnnouncementBySlug = async (req, res) => {
  const { slug } = req.params;
  
  const announcement = await Announcement.findOne({ 
    slug, 
    active: true 
  }).populate('author', 'username firstName lastName');
  
  if (!announcement) {
    return res.status(404).json({ message: 'Announcement not found' });
  }
  
  res.status(200).json(announcement);
};
```

## SEO Benefits

### 1. Search Engine Optimization

**URL Structure:**
- **Descriptive**: URLs describe content clearly
- **Keyword-Rich**: Include relevant keywords
- **Hierarchical**: Logical URL structure
- **Consistent**: Uniform formatting across site

**Examples:**
```
Good: /announcements/summer-coffee-festival-2024
Bad:  /announcements/507f1f77bcf86cd799439011

Good: /menu/organic-fair-trade-espresso
Bad:  /menu/item?id=123
```

### 2. User Experience

**Benefits:**
- **Readable URLs**: Users can understand content from URL
- **Shareable**: Easy to share and remember
- **Predictable**: Users can guess URL structure
- **Bookmarkable**: Meaningful URLs for bookmarking

## Conflict Resolution Strategies

### 1. Incremental Numbering

**Announcement Model:**
```javascript
// First: "summer-promotion"
// Second: "summer-promotion-1"
// Third: "summer-promotion-2"
```

### 2. Random Suffix (Menu Items)

**Menu Model:**
```javascript
// First: "espresso-special"
// Second: "espresso-special-847"
// Third: "espresso-special-293"
```

### 3. Timestamp-Based (Alternative)

```javascript
const createTimestampSlug = (title) => {
  const baseSlug = slugify(title, { lower: true, strict: true, locale: 'tr' });
  const timestamp = Date.now();
  return `${baseSlug}-${timestamp}`;
};
```

## Update Handling

### 1. Title/Name Changes

When titles or names change, slugs are automatically updated:

```javascript
export const updateAnnouncement = async (req, res) => {
  const { id } = req.params;
  const { title, content, summary } = req.body;
  
  const announcement = await Announcement.findById(id);
  
  // Check if title changed
  let slug = announcement.slug;
  if (title && title !== announcement.title) {
    slug = await createUniqueSlug(title);
  }
  
  const updatedAnnouncement = await Announcement.findByIdAndUpdate(
    id,
    { title, slug, content, summary, updatedAt: Date.now() },
    { new: true }
  );
  
  res.status(200).json(updatedAnnouncement);
};
```

### 2. Slug Preservation

**Considerations:**
- **SEO Impact**: Changing slugs affects search rankings
- **Bookmarks**: Users' bookmarks may break
- **External Links**: Links from other sites may break

**Best Practices:**
- Implement redirects for old slugs
- Consider slug history tracking
- Warn users about URL changes

## Validation and Constraints

### 1. Database Constraints

```javascript
const schema = new mongoose.Schema({
  slug: { 
    type: String, 
    required: true, 
    unique: true,
    index: true,
    minlength: 1,
    maxlength: 200,
    match: /^[a-z0-9-]+$/
  }
});
```

### 2. Input Validation

```javascript
const validateSlugInput = (text) => {
  if (!text || typeof text !== 'string') {
    throw new Error('Invalid input for slug generation');
  }
  
  if (text.length > 200) {
    throw new Error('Input too long for slug generation');
  }
  
  if (text.trim().length === 0) {
    throw new Error('Input cannot be empty');
  }
  
  return true;
};
```

## Performance Considerations

### 1. Database Indexing

```javascript
// Ensure slug fields are indexed
schema.index({ slug: 1 });
schema.index({ slug: 1, active: 1 });
```

### 2. Caching Strategies

```javascript
// Cache frequently accessed slugs
const slugCache = new Map();

const getCachedBySlug = async (slug, Model) => {
  if (slugCache.has(slug)) {
    return slugCache.get(slug);
  }
  
  const result = await Model.findOne({ slug, active: true });
  
  if (result) {
    slugCache.set(slug, result);
  }
  
  return result;
};
```

### 3. Batch Operations

```javascript
// Efficient slug generation for bulk operations
const generateBulkSlugs = async (items, Model) => {
  const slugs = [];
  const existingSlugs = new Set();
  
  // Get all existing slugs in one query
  const existing = await Model.find({}, { slug: 1 });
  existing.forEach(item => existingSlugs.add(item.slug));
  
  for (const item of items) {
    let slug = slugify(item.title, { lower: true, strict: true, locale: 'tr' });
    let counter = 1;
    
    while (existingSlugs.has(slug)) {
      slug = `${slugify(item.title, { lower: true, strict: true, locale: 'tr' })}-${counter}`;
      counter++;
    }
    
    existingSlugs.add(slug);
    slugs.push(slug);
  }
  
  return slugs;
};
```

## Testing

### 1. Unit Tests

```javascript
describe('Slug Generation', () => {
  it('should generate basic slug', () => {
    const slug = slugify('Hello World', { lower: true, strict: true, locale: 'tr' });
    expect(slug).toBe('hello-world');
  });

  it('should handle Turkish characters', () => {
    const slug = slugify('Çay ve Kahve', { lower: true, strict: true, locale: 'tr' });
    expect(slug).toBe('cay-ve-kahve');
  });

  it('should remove special characters', () => {
    const slug = slugify('Coffee & Tea!', { lower: true, strict: true, locale: 'tr' });
    expect(slug).toBe('coffee-and-tea');
  });
});
```

### 2. Integration Tests

```javascript
describe('Unique Slug Generation', () => {
  it('should create unique slugs for duplicate titles', async () => {
    const announcement1 = await Announcement.create({
      title: 'Test Announcement',
      content: 'Content 1',
      author: userId
    });

    const announcement2 = await Announcement.create({
      title: 'Test Announcement',
      content: 'Content 2',
      author: userId
    });

    expect(announcement1.slug).toBe('test-announcement');
    expect(announcement2.slug).toBe('test-announcement-1');
  });
});
```

## Troubleshooting

### Common Issues

#### 1. Duplicate Slug Errors
**Symptoms**: Database unique constraint violations
**Causes**: Race conditions, failed uniqueness checks
**Solutions**: Implement proper locking, retry mechanisms

#### 2. Empty Slugs
**Symptoms**: Validation errors for empty slugs
**Causes**: Input with only special characters
**Solutions**: Fallback slug generation, input validation

#### 3. Long Slugs
**Symptoms**: URLs too long, database errors
**Causes**: Very long titles
**Solutions**: Truncate input, implement length limits

### Debug Commands

```bash
# Test slug generation
node -e "
const slugify = require('slugify');
console.log(slugify('Test Title', { lower: true, strict: true, locale: 'tr' }));
"

# Check for duplicate slugs
mongo l-cafe --eval "
db.announcements.aggregate([
  { \$group: { _id: '\$slug', count: { \$sum: 1 } } },
  { \$match: { count: { \$gt: 1 } } }
])
"
```

## Best Practices

### 1. Consistency
- Use same slugify configuration across all models
- Implement consistent conflict resolution
- Maintain uniform URL patterns

### 2. Performance
- Index slug fields properly
- Cache frequently accessed slugs
- Optimize uniqueness checks

### 3. SEO
- Keep slugs descriptive and keyword-rich
- Avoid frequent slug changes
- Implement proper redirects when needed

### 4. User Experience
- Make URLs readable and predictable
- Handle edge cases gracefully
- Provide meaningful error messages

## Conclusion

The Slugify system in L Café provides a robust, SEO-friendly URL generation mechanism that handles internationalization, prevents conflicts, and maintains consistency across all content types. The implementation balances performance, usability, and maintainability while supporting the café's multilingual content needs.

Key strengths:
- **SEO-Optimized**: Creates search engine friendly URLs
- **Conflict-Free**: Automatic handling of duplicate slugs
- **International**: Supports Turkish and other character sets
- **Consistent**: Uniform implementation across all models
- **Performant**: Optimized for database operations and caching
