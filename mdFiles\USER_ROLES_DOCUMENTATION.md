# User Roles System Documentation

## Overview

The User Roles system in L Café implements a comprehensive Role-Based Access Control (RBAC) mechanism that governs user permissions, feature access, and data security across the entire application. The system supports three distinct roles with hierarchical permissions and granular access control.

## Role Hierarchy

### 1. Admin Role (Highest Privilege)

**Full System Access:**
- **User Management**: Create, update, delete, and manage all users
- **Content Management**: Full CRUD operations on all content types
- **System Administration**: Access to system settings and configuration
- **Analytics and Monitoring**: Complete access to visitor data and system metrics
- **Security Management**: Manage authentication and authorization settings

**Exclusive Permissions:**
- Create and delete user accounts
- Modify user roles and permissions
- Access system-wide analytics
- Configure application settings
- Manage security policies
- View system logs and monitoring data

### 2. Staff Role (Content Management)

**Content Management Access:**
- **Announcements**: Create, edit, and publish announcements
- **Menu Management**: Manage food and beverage items
- **Gallery Management**: Upload and organize images
- **Q&A Management**: Respond to customer questions
- **Dashboard Access**: View content statistics and metrics

**Limited Permissions:**
- Cannot manage other users
- Cannot access system settings
- Cannot view detailed analytics
- Cannot modify security settings
- Can only edit content they created (with some exceptions)

### 3. Customer Role (Public Access)

**Basic User Access:**
- **Profile Management**: Edit own profile information
- **Public Content**: View announcements, menu, gallery
- **Q&A Interaction**: Submit questions and view answers
- **Account Settings**: Change password and personal details

**Restricted Access:**
- No dashboard access
- Cannot create or edit content
- Cannot view other user information
- Cannot access administrative features

## Implementation Architecture

### 1. Database Schema

**User Model (`backend/models/user.model.js`):**
```javascript
const userSchema = new mongoose.Schema({
  username: { 
    type: String, 
    required: true, 
    unique: true,
    minlength: 3,
    maxlength: 30
  },
  email: { 
    type: String, 
    required: true, 
    unique: true,
    lowercase: true
  },
  password: { 
    type: String, 
    required: true,
    minlength: 6
  },
  role: { 
    type: String, 
    enum: ['admin', 'staff', 'customer'], 
    default: 'customer',
    required: true
  },
  active: { 
    type: Boolean, 
    default: true 
  },
  firstName: { 
    type: String,
    maxlength: 50
  },
  lastName: { 
    type: String,
    maxlength: 50
  },
  lastLogin: { 
    type: Date 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Indexes for performance
userSchema.index({ role: 1, active: 1 });
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
```

### 2. Middleware Implementation

**Complete Middleware Implementation:**

**Authentication Middleware (`backend/middleware/auth.middleware.js`):**
```javascript
// middleware/auth.middleware.js
import jwt from 'jsonwebtoken';

const authMiddleware = (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;

    // Check if Authorization header exists and has the correct format
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'No token provided, authorization denied' });
    }

    // Extract the token (remove "Bearer " prefix)
    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ message: 'No token provided, authorization denied' });
    }

    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Attach user info to request object
    req.user = decoded;

    // Proceed to the next middleware or route handler
    next();
  } catch (error) {
    // Handle different error types
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }

    res.status(500).json({ message: 'Server error during authentication', error: error.message });
  }
};

export default authMiddleware;
```

**Role-Based Authorization Middleware (`backend/middleware/role.middleware.js`):**
```javascript
// middleware/role.middleware.js
const roleMiddleware = (roles = []) => {
    return (req, res, next) => {
      // Convert string to array if single role is provided
      if (typeof roles === 'string') {
        roles = [roles];
      }

      // Check if user exists (should be attached by auth middleware)
      if (!req.user) {
        return res.status(401).json({ message: 'Unauthorized - No user found' });
      }

      // Check if user role is included in allowed roles
      if (roles.length && !roles.includes(req.user.role)) {
        return res.status(403).json({
          message: 'Forbidden - You do not have permission to access this resource'
        });
      }

      // User has required role, proceed to next middleware
      next();
    };
  };

  export default roleMiddleware;
```

**Usage Examples in Routes:**
```javascript
// Admin-only routes
router.get('/users', [authMiddleware, roleMiddleware(['admin'])], getAllUsers);

// Staff and Admin routes
router.post('/announcements', [authMiddleware, roleMiddleware(['admin', 'staff'])], createAnnouncement);

// Single role requirement
router.delete('/users/:id', [authMiddleware, roleMiddleware('admin')], deleteUser);
```

### 3. Frontend Role Management

**Authentication Context (`src/contexts/NewAuthContext.tsx`):**
```typescript
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isStaff: boolean;
  isCustomer: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Role-based computed properties
  const isAdmin = user?.role === 'admin';
  const isStaff = user?.role === 'staff' || user?.role === 'admin';
  const isCustomer = user?.role === 'customer';

  const contextValue = {
    user,
    isAuthenticated,
    isAdmin,
    isStaff,
    isCustomer,
    login,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

**Role-Based Route Protection:**
```typescript
// Protected route component
const ProtectedRoute: React.FC<{
  children: React.ReactNode;
  requiredRole?: 'admin' | 'staff' | 'customer';
  fallback?: React.ReactNode;
}> = ({ children, requiredRole, fallback }) => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole) {
    const hasPermission = checkRolePermission(user?.role, requiredRole);
    if (!hasPermission) {
      return fallback || <AccessDenied />;
    }
  }

  return <>{children}</>;
};

// Role permission checker
const checkRolePermission = (userRole: string, requiredRole: string): boolean => {
  const roleHierarchy = {
    admin: 3,
    staff: 2,
    customer: 1
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};
```

## Permission Matrix

### Detailed Access Control

| Feature/Action | Admin | Staff | Customer | Notes |
|----------------|-------|-------|----------|-------|
| **Authentication** |
| Login/Logout | ✅ | ✅ | ✅ | All users |
| Change Own Password | ✅ | ✅ | ✅ | All users |
| **Dashboard Access** |
| View Dashboard | ✅ | ✅ | ❌ | Staff+ only |
| View Statistics | ✅ | ✅ | ❌ | Staff+ only |
| **User Management** |
| View All Users | ✅ | ❌ | ❌ | Admin only |
| Create Users | ✅ | ❌ | ❌ | Admin only |
| Edit User Profiles | ✅ | ❌ | ❌ | Admin only |
| Change User Roles | ✅ | ❌ | ❌ | Admin only |
| Deactivate Users | ✅ | ❌ | ❌ | Admin only |
| Delete Users | ✅ | ❌ | ❌ | Admin only |
| Reset User Passwords | ✅ | ❌ | ❌ | Admin only |
| **Content Management** |
| View Announcements | ✅ | ✅ | ✅ | All users (public) |
| Create Announcements | ✅ | ✅ | ❌ | Staff+ only |
| Edit Announcements | ✅ | ✅* | ❌ | *Own content only |
| Delete Announcements | ✅ | ✅* | ❌ | *Own content only |
| Publish/Unpublish | ✅ | ✅ | ❌ | Staff+ only |
| **Menu Management** |
| View Menu | ✅ | ✅ | ✅ | All users (public) |
| Create Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Edit Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Delete Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Manage Categories | ✅ | ✅ | ❌ | Staff+ only |
| **Gallery Management** |
| View Gallery | ✅ | ✅ | ✅ | All users (public) |
| Upload Images | ✅ | ✅ | ❌ | Staff+ only |
| Edit Image Details | ✅ | ✅ | ❌ | Staff+ only |
| Delete Images | ✅ | ✅ | ❌ | Staff+ only |
| Manage Categories | ✅ | ✅ | ❌ | Staff+ only |
| **Q&A Management** |
| View Q&A | ✅ | ✅ | ✅ | All users (public) |
| Submit Questions | ✅ | ✅ | ✅ | All users |
| Answer Questions | ✅ | ✅ | ❌ | Staff+ only |
| Moderate Questions | ✅ | ✅ | ❌ | Staff+ only |
| Delete Q&A Items | ✅ | ✅ | ❌ | Staff+ only |
| **Analytics & Monitoring** |
| View Visitor Analytics | ✅ | ❌ | ❌ | Admin only |
| View System Logs | ✅ | ❌ | ❌ | Admin only |
| Monitor Performance | ✅ | ❌ | ❌ | Admin only |
| **System Settings** |
| Modify App Settings | ✅ | ❌ | ❌ | Admin only |
| Manage Security | ✅ | ❌ | ❌ | Admin only |
| Database Management | ✅ | ❌ | ❌ | Admin only |
| **Profile Management** |
| View Own Profile | ✅ | ✅ | ✅ | All users |
| Edit Own Profile | ✅ | ✅ | ✅ | All users |
| View Other Profiles | ✅ | ❌ | ❌ | Admin only |

## Role Management Operations

### 1. User Creation with Roles

**Complete User Controller Implementation (`backend/controllers/user.controller.js`):**

```javascript
// controllers/user.controller.js
import User from '../models/user.model.js';
import mongoose from 'mongoose';
import bcrypt from 'bcrypt';

// Get all users (admin only)
export const getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10, active } = req.query;

        // Build filter object
        const filter = {};
        if (active !== undefined) {
            filter.active = active === 'true';
        }

        // Calculate how many documents to skip
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get users with pagination, excluding password field
        const users = await User.find(filter)
            .select('-password')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const total = await User.countDocuments(filter);

        res.status(200).json({
            users,
            totalPages: Math.ceil(total / parseInt(limit)),
            currentPage: parseInt(page),
            total
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error fetching users',
            error: error.message
        });
    }
};

// Get user by ID (admin only)
export const getUserById = async (req, res) => {
    try {
        const { id } = req.params;

        // Validate object ID
        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ message: 'Invalid user ID' });
        }

        const user = await User.findById(id).select('-password');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json(user);
    } catch (error) {
        res.status(500).json({
            message: 'Error fetching user',
            error: error.message
        });
    }
};

// Update user (admin only)
export const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { username, email, firstName, lastName, role, active } = req.body;

        // Validate object ID
        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ message: 'Invalid user ID' });
        }

        // Find user
        const user = await User.findById(id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Check for unique username and email if being changed
        if (username && username !== user.username) {
            const existingUsername = await User.findOne({ username });
            if (existingUsername) {
                return res.status(400).json({ message: 'Username already exists' });
            }
        }

        if (email && email !== user.email) {
            const existingEmail = await User.findOne({ email });
            if (existingEmail) {
                return res.status(400).json({ message: 'Email already exists' });
            }
        }

        // Validate role if provided
        if (role) {
            const validRoles = ['admin', 'staff', 'customer'];
            if (!validRoles.includes(role)) {
                return res.status(400).json({
                    message: 'Invalid role specified',
                    validRoles
                });
            }
        }

        // Update user
        const updatedUser = await User.findByIdAndUpdate(
            id,
            {
                username: username || user.username,
                email: email || user.email,
                firstName: firstName !== undefined ? firstName : user.firstName,
                lastName: lastName !== undefined ? lastName : user.lastName,
                role: role || user.role,
                active: active !== undefined ? active : user.active,
                updatedAt: Date.now()
            },
            { new: true }
        ).select('-password');

        res.status(200).json({
            message: 'User updated successfully',
            user: updatedUser
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error updating user',
            error: error.message
        });
    }
};

// Delete user (admin only)
export const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Validate object ID
        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ message: 'Invalid user ID' });
        }

        // Prevent self-deletion
        if (req.user.id === id) {
            return res.status(400).json({ message: 'Cannot delete your own account' });
        }

        const deletedUser = await User.findByIdAndDelete(id);

        if (!deletedUser) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json({
            message: 'User deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error deleting user',
            error: error.message
        });
    }
};

// Change user password (admin only)
export const changeUserPassword = async (req, res) => {
    try {
        const { id } = req.params;
        const { newPassword } = req.body;

        // Validate object ID
        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ message: 'Invalid user ID' });
        }

        // Validate password
        if (!newPassword || newPassword.length < 6) {
            return res.status(400).json({
                message: 'Password must be at least 6 characters long'
            });
        }

        // Find user
        const user = await User.findById(id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Update password (will be hashed by pre-save hook)
        user.password = newPassword;
        user.updatedAt = Date.now();
        await user.save();

        res.status(200).json({
            message: 'Password updated successfully'
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error updating password',
            error: error.message
        });
    }
};

// Create user (admin only) - for admin panel user creation
export const createUser = async (req, res) => {
    try {
        const { username, email, password, role, firstName, lastName } = req.body;

        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                message: 'Username, email, and password are required'
            });
        }

        // Validate role
        const validRoles = ['admin', 'staff', 'customer'];
        if (role && !validRoles.includes(role)) {
            return res.status(400).json({
                message: 'Invalid role specified',
                validRoles
            });
        }

        // Check if user already exists
        const existingUser = await User.findOne({
            $or: [{ email }, { username }]
        });

        if (existingUser) {
            return res.status(400).json({
                message: 'User already exists with this email or username'
            });
        }

        // Create user (password will be hashed by pre-save hook)
        const user = new User({
            username,
            email,
            password,
            role: role || 'customer',
            firstName,
            lastName,
            active: true
        });

        await user.save();

        // Return user without password
        const userResponse = user.toObject();
        delete userResponse.password;

        res.status(201).json({
            message: 'User created successfully',
            user: userResponse
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error creating user',
            error: error.message
        });
    }
};

// Get user statistics (admin only)
export const getUserStats = async (req, res) => {
    try {
        const [total, active, byRole] = await Promise.all([
            User.countDocuments(),
            User.countDocuments({ active: true }),
            User.aggregate([
                {
                    $group: {
                        _id: '$role',
                        count: { $sum: 1 }
                    }
                }
            ])
        ]);

        const roleStats = {};
        byRole.forEach(item => {
            roleStats[item._id] = item.count;
        });

        res.json({
            total,
            active,
            byRole: roleStats
        });
    } catch (error) {
        res.status(500).json({
            message: 'Error fetching user statistics',
            error: error.message
        });
    }
};
```

### 2. Role Update Operations

**Update User Role:**
```javascript
export const updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // Validate role
    const validRoles = ['admin', 'staff', 'customer'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ 
        message: 'Invalid role specified',
        validRoles 
      });
    }

    // Prevent self-demotion for admins
    if (req.user.id === id && req.user.role === 'admin' && role !== 'admin') {
      return res.status(400).json({ 
        message: 'Cannot demote yourself from admin role' 
      });
    }

    const user = await User.findByIdAndUpdate(
      id,
      { role, updatedAt: Date.now() },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      message: 'User role updated successfully',
      user
    });
  } catch (error) {
    res.status(500).json({ 
      message: 'Error updating user role', 
      error: error.message 
    });
  }
};
```

### 3. Frontend Role Management

**User Management Component:**
```typescript
const UserManagement: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      await userService.updateUserRole(userId, newRole);
      
      // Update local state
      setUsers(users.map(user => 
        user._id === userId 
          ? { ...user, role: newRole }
          : user
      ));

      toast.success('User role updated successfully');
    } catch (error) {
      toast.error('Failed to update user role');
    }
  };

  const RoleSelector: React.FC<{ user: User }> = ({ user }) => (
    <Select
      value={user.role}
      onValueChange={(newRole) => handleRoleChange(user._id, newRole)}
      disabled={user._id === currentUser?.id} // Prevent self-modification
    >
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="customer">Customer</SelectItem>
        <SelectItem value="staff">Staff</SelectItem>
        <SelectItem value="admin">Admin</SelectItem>
      </SelectContent>
    </Select>
  );

  return (
    <div className="user-management">
      {/* User list with role management */}
    </div>
  );
};
```

## Security Considerations

### 1. Role Validation

**Server-Side Validation:**
- All role changes validated on backend
- Middleware enforces role requirements
- Database constraints prevent invalid roles
- Audit logging for role changes

**Client-Side Protection:**
- UI elements hidden based on roles
- Route protection prevents unauthorized access
- API calls include role validation
- Real-time permission checking

### 2. Privilege Escalation Prevention

**Security Measures:**
- Users cannot modify their own roles
- Admin role required for user management
- Role changes require CSRF protection
- Audit trail for all role modifications

**Implementation:**
```javascript
// Prevent self-role modification
if (req.user.id === targetUserId && req.body.role !== req.user.role) {
  return res.status(403).json({ 
    message: 'Cannot modify your own role' 
  });
}

// Ensure only admins can create other admins
if (req.body.role === 'admin' && req.user.role !== 'admin') {
  return res.status(403).json({ 
    message: 'Only admins can create admin users' 
  });
}
```

### 3. Session Management

**Role-Based Sessions:**
- JWT tokens include role information
- Token validation checks role consistency
- Role changes invalidate existing tokens
- Session timeout based on role sensitivity

## Testing Role-Based Access

### 1. Unit Tests

**Role Middleware Tests:**
```javascript
describe('Role Middleware', () => {
  it('should allow admin access to admin routes', async () => {
    const req = { user: { role: 'admin' } };
    const res = {};
    const next = jest.fn();

    adminMiddleware(req, res, next);
    expect(next).toHaveBeenCalled();
  });

  it('should deny staff access to admin routes', async () => {
    const req = { user: { role: 'staff' } };
    const res = { 
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    const next = jest.fn();

    adminMiddleware(req, res, next);
    expect(res.status).toHaveBeenCalledWith(403);
    expect(next).not.toHaveBeenCalled();
  });
});
```

### 2. Integration Tests

**Role-Based API Tests:**
```javascript
describe('User Management API', () => {
  it('should allow admin to create users', async () => {
    const response = await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        role: 'staff'
      })
      .expect(201);

    expect(response.body.user.role).toBe('staff');
  });

  it('should deny staff from creating users', async () => {
    await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${staffToken}`)
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(403);
  });
});
```

## Best Practices

### 1. Role Design Principles

**Principle of Least Privilege:**
- Grant minimum necessary permissions
- Regular permission audits
- Role-based feature access
- Granular permission control

**Role Hierarchy:**
- Clear role inheritance
- Logical permission escalation
- Consistent role naming
- Well-defined boundaries

### 2. Implementation Guidelines

**Backend Security:**
- Always validate roles server-side
- Use middleware for consistent enforcement
- Implement audit logging
- Regular security reviews

**Frontend UX:**
- Hide unavailable features
- Provide clear access feedback
- Graceful permission errors
- Role-appropriate interfaces

### 3. Maintenance and Monitoring

**Regular Audits:**
- Review user roles quarterly
- Monitor privilege escalation attempts
- Audit role change logs
- Validate permission consistency

**Performance Monitoring:**
- Track role-based query performance
- Monitor authentication overhead
- Optimize role checking logic
- Cache role permissions appropriately

## Conclusion

The User Roles system in L Café provides a robust, secure, and scalable foundation for access control and user management. The implementation follows security best practices while maintaining usability and performance across all user types.

Key strengths:
- **Comprehensive RBAC**: Full role-based access control
- **Security-First Design**: Multiple layers of protection
- **Scalable Architecture**: Easy to extend and modify
- **User-Friendly Interface**: Intuitive role management
- **Performance Optimized**: Efficient permission checking
- **Audit-Ready**: Complete logging and monitoring
