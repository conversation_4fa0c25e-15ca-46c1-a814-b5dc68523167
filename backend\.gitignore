
# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Build files
dist/
build/

# Logs
logs/
*.log

# OS specific
.DS_Store
Thumbs.db

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo

# Uploads directory content (but keep the directory structure)
public/uploads/gallery/*
!public/uploads/gallery/.gitkeep
public/uploads/gallery/thumbnails/*
!public/uploads/gallery/thumbnails/.gitkeep
public/uploads/menu/*
!public/uploads/menu/.gitkeep
