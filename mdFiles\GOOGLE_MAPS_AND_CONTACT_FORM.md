# Google Maps and Contact Form Implementation

## Overview
This document explains how Google Maps and the contact form functionality are implemented in the L Café website.

## Google Maps Implementation

### Location Component (`src/components/Location.tsx`)

The Google Maps integration is implemented using an embedded iframe approach, which is simple and doesn't require API keys for basic functionality.

#### Implementation Details:

```tsx
<iframe 
  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.9663095343033!2d-74.0059556846331!3d40.74147877932915!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c259bf5c1654f3%3A0xc80f9cfce5383d5d!2sNew%20York%2C%20NY!5e0!3m2!1sen!2sus!4v1650000000000!5m2!1sen!2sus" 
  width="100%" 
  height="100%" 
  style={{ border: 0 }} 
  allowFullScreen={true} 
  loading="lazy" 
  referrerPolicy="no-referrer-when-downgrade"
/>
```

#### Key Features:
- **Responsive Design**: Uses 100% width and height to fit container
- **Lazy Loading**: Improves page performance by loading map only when needed
- **No API Key Required**: Uses Google's embed URL which doesn't require authentication
- **Full Screen Support**: Users can expand to full screen view
- **Security**: Uses `no-referrer-when-downgrade` policy

#### How to Update the Map Location:

1. Go to [Google Maps](https://maps.google.com)
2. Search for your business location
3. Click "Share" button
4. Select "Embed a map" tab
5. Copy the iframe src URL
6. Replace the `src` attribute in the Location component

#### Current Location Settings:
- **Address**: 123 Coffee Street, Downtown, City 10001
- **Coordinates**: Latitude 40.74147877932915, Longitude -74.0059556846331
- **Map Type**: Standard roadmap view
- **Zoom Level**: Automatically optimized

## Contact Form Implementation

### Architecture Overview

The contact form system consists of multiple layers:

1. **Frontend Components**: Form UI and validation
2. **Service Layer**: API communication
3. **Backend Controllers**: Request handling and validation
4. **Database**: Message storage and management

### Frontend Components

#### 1. Contact Component (`src/components/Contact.tsx`)
- Main contact form on the homepage
- Uses React Hook Form for validation
- Integrates with contact service for submission

#### 2. ContactForm Component (`src/components/ContactForm.tsx`)
- Reusable contact form component
- Used in the dedicated Contact page
- Same functionality as Contact component

#### 3. Contact Page (`src/pages/Contact.tsx`)
- Dedicated contact page with additional features
- Includes both contact form and Q&A submission
- Displays business information and hours

### Form Validation Schema

```typescript
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" })
});
```

### Service Layer (`src/services/contact.service.ts`)

#### Key Functions:
- `submitContactForm(formData)`: Submits contact form to backend
- `getContactMessages()`: Retrieves messages for admin dashboard
- `updateContactStatus()`: Updates message status (admin only)
- `deleteContactMessage()`: Deletes messages (admin only)

#### CSRF Protection:
All form submissions include CSRF token for security:
```typescript
await authService.getCsrfToken();
return api.post('/contact/submit', formData);
```

### Backend Implementation

#### Controller (`backend/controllers/contact.controller.js`)

**Validation Rules:**
- Name: 2-100 characters
- Email: Valid email format
- Message: 10-2000 characters
- IP address and user agent tracking for security

**Database Schema:**
```javascript
{
  name: String (required),
  email: String (required),
  message: String (required),
  status: String (default: 'new'),
  ipAddress: String,
  userAgent: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### API Endpoints:

**Public Endpoints:**
- `POST /api/contact/submit` - Submit contact form

**Admin Endpoints:**
- `GET /api/contact/messages` - Get all messages (paginated)
- `PUT /api/contact/:id/status` - Update message status
- `DELETE /api/contact/:id` - Delete message
- `GET /api/contact/stats` - Get contact statistics

### Admin Dashboard Integration

#### Dashboard Component (`src/components/dashboard/DashboardContacts.tsx`)

**Features:**
- View all contact messages
- Filter by status (new, in-progress, resolved)
- Search functionality
- Update message status
- Delete messages
- View detailed message information

**Status Management:**
- `new`: Newly received messages
- `in-progress`: Messages being handled
- `resolved`: Completed messages

### Security Features

1. **CSRF Protection**: All form submissions require valid CSRF tokens
2. **Input Validation**: Both frontend and backend validation
3. **Rate Limiting**: Prevents spam submissions
4. **IP Tracking**: Logs IP addresses for security monitoring
5. **Sanitization**: Input sanitization to prevent XSS attacks

### Error Handling

#### Frontend Error Handling:
```typescript
try {
  await contactService.submitContactForm(values);
  // Success toast
} catch (error: any) {
  toast({
    variant: "destructive",
    title: "Error",
    description: error.response?.data?.message || "Failed to send message."
  });
}
```

#### Backend Error Responses:
- 400: Validation errors
- 500: Server errors
- Detailed error messages for debugging

### Usage Examples

#### Basic Form Submission:
```typescript
const handleSubmit = async (values: FormValues) => {
  setIsSubmitting(true);
  try {
    await contactService.submitContactForm(values);
    toast({ title: "Message Sent", description: "We'll get back to you soon." });
    form.reset();
  } catch (error) {
    // Handle error
  } finally {
    setIsSubmitting(false);
  }
};
```

#### Admin Message Management:
```typescript
// Get messages with pagination and filtering
const { data } = useQuery({
  queryKey: ['contact-messages', page, status, search],
  queryFn: () => contactService.getContactMessages(page, 10, status, search)
});

// Update message status
await contactService.updateContactStatus(messageId, 'resolved', 'Issue resolved');
```

## Maintenance and Updates

### Regular Tasks:
1. **Monitor Contact Messages**: Check admin dashboard regularly
2. **Update Map Location**: If business moves, update embed URL
3. **Review Form Analytics**: Monitor submission rates and errors
4. **Security Updates**: Keep dependencies updated

### Troubleshooting:

**Map Not Loading:**
- Check internet connection
- Verify embed URL is correct
- Ensure no ad blockers are interfering

**Form Submission Errors:**
- Check backend server status
- Verify CSRF token generation
- Review network requests in browser dev tools
- Check backend logs for detailed errors

**Admin Dashboard Issues:**
- Verify user has admin permissions
- Check authentication status
- Review API endpoint responses
