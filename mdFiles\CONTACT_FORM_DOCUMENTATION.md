# Contact Form System Documentation

## Overview

The Contact Form system provides a comprehensive solution for managing customer inquiries and feedback in L Café. It includes a public contact form for customers to submit messages and a complete admin interface for managing, tracking, and responding to these messages.

## Architecture

### Database Model

#### Contact Schema (`backend/models/contact.model.js`)

```javascript
const contactSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 100
  },
  email: { 
    type: String, 
    required: true,
    lowercase: true,
    trim: true,
    maxlength: 255
  },
  message: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 2000
  },
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'archived'],
    default: 'new'
  },
  ipAddress: { type: String },
  userAgent: { type: String },
  replied: { type: Boolean, default: false },
  repliedAt: { type: Date },
  repliedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  notes: { type: String, maxlength: 1000 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Key Features:**
- **Status Tracking**: Four-stage workflow (new → read → replied → archived)
- **User Association**: Links replies to admin users
- **Metadata Collection**: IP address and user agent for analytics
- **Notes System**: Internal notes for admin use
- **Validation**: Input length limits and email validation

### Backend Implementation

#### API Endpoints (`backend/routes/contact.routes.js`)

**Public Routes:**
- `POST /api/contact/submit` - Submit contact form (CSRF protected)

**Admin Routes (Authentication Required):**
- `GET /api/contact` - Get all contact messages with pagination
- `GET /api/contact/stats` - Get contact statistics
- `GET /api/contact/:id` - Get single contact message
- `PUT /api/contact/:id/status` - Update message status
- `DELETE /api/contact/:id` - Delete contact message

#### Controller Functions (`backend/controllers/contact.controller.js`)

**Complete Controller Implementation:**

```javascript
// controllers/contact.controller.js
import Contact from '../models/contact.model.js';
import mongoose from 'mongoose';

// Get all contact messages (admin only)
export const getContactMessages = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const search = req.query.search;

    // Build query
    let query = {};

    if (status && status !== 'all') {
      query.status = status;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;

    // Get messages with pagination
    const messages = await Contact.find(query)
      .populate('repliedBy', 'username firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Contact.countDocuments(query);

    res.json({
      messages,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        total,
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching contact messages',
      error: error.message
    });
  }
};

// Get single contact message (admin only)
export const getContactMessage = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid contact message ID' });
    }

    const message = await Contact.findById(id)
      .populate('repliedBy', 'username firstName lastName');

    if (!message) {
      return res.status(404).json({ message: 'Contact message not found' });
    }

    res.json(message);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching contact message',
      error: error.message
    });
  }
};

// Submit contact form (public)
export const submitContactForm = async (req, res) => {
  try {
    const { name, email, message } = req.body;

    // Comprehensive validation
    if (!name || !email || !message) {
      return res.status(400).json({
        message: 'Name, email, and message are required'
      });
    }

    // Additional validation
    if (name.trim().length < 2 || name.trim().length > 100) {
      return res.status(400).json({
        message: 'Name must be between 2 and 100 characters'
      });
    }

    if (message.trim().length < 10 || message.trim().length > 2000) {
      return res.status(400).json({
        message: 'Message must be between 10 and 2000 characters'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        message: 'Please enter a valid email address'
      });
    }

    // Create contact message
    const contactMessage = new Contact({
      name: name.trim(),
      email: email.trim().toLowerCase(),
      message: message.trim(),
      status: 'new',
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    });

    await contactMessage.save();

    res.status(201).json({
      message: 'Thank you for your message! We\'ll get back to you soon.',
      id: contactMessage._id
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error submitting contact form',
      error: error.message
    });
  }
};

// Update contact message status (admin only)
export const updateContactStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid contact message ID' });
    }

    const validStatuses = ['new', 'read', 'replied', 'archived'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    const updateData = {
      status,
      updatedAt: Date.now()
    };

    if (notes !== undefined) {
      updateData.notes = notes;
    }

    if (status === 'replied') {
      updateData.replied = true;
      updateData.repliedAt = Date.now();
      updateData.repliedBy = req.user.id;
    }

    const updatedMessage = await Contact.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('repliedBy', 'username firstName lastName');

    if (!updatedMessage) {
      return res.status(404).json({ message: 'Contact message not found' });
    }

    res.json({
      message: 'Contact message status updated successfully',
      contact: updatedMessage
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error updating contact message status',
      error: error.message
    });
  }
};

// Delete contact message (admin only)
export const deleteContactMessage = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid contact message ID' });
    }

    const deletedMessage = await Contact.findByIdAndDelete(id);

    if (!deletedMessage) {
      return res.status(404).json({ message: 'Contact message not found' });
    }

    res.json({ message: 'Contact message deleted successfully' });
  } catch (error) {
    res.status(500).json({
      message: 'Error deleting contact message',
      error: error.message
    });
  }
};

// Get contact statistics (admin only)
export const getContactStats = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [total, todayCount, statusCounts] = await Promise.all([
      Contact.countDocuments(),
      Contact.countDocuments({ createdAt: { $gte: today } }),
      Contact.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    const byStatus = {};
    statusCounts.forEach(item => {
      byStatus[item._id] = item.count;
    });

    res.json({
      total,
      today: todayCount,
      byStatus
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching contact statistics',
      error: error.message
    });
  }
};
```

### Frontend Implementation

#### Contact Form Component (`src/components/ContactForm.tsx`)

**Complete Contact Form Implementation:**

```typescript
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import contactService from "@/services/contact.service";

// Validation schema
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" })
});

type FormValues = z.infer<typeof formSchema>;

const ContactForm = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      message: ""
    },
  });

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);

    try {
      await contactService.submitContactForm(values);

      toast({
        title: "Message Sent",
        description: "Thank you for your message! We'll get back to you soon.",
      });

      // Reset form
      form.reset();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.response?.data?.message || "Failed to send message. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Your name"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Message</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Your message..."
                    className="min-h-[120px]"
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Sending..." : "Send Message"}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default ContactForm;
```

#### Contact Service (`src/services/contact.service.ts`)

**Complete Service Implementation:**

```typescript
// services/contact.service.ts
import api from './api';
import authService from './auth.service';

export interface ContactMessage {
  _id: string;
  name: string;
  email: string;
  message: string;
  status: 'new' | 'read' | 'replied' | 'archived';
  ipAddress?: string;
  userAgent?: string;
  replied: boolean;
  repliedAt?: string;
  repliedBy?: {
    _id: string;
    username: string;
    firstName?: string;
    lastName?: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

export interface ContactStats {
  total: number;
  today: number;
  byStatus: {
    new?: number;
    read?: number;
    replied?: number;
    archived?: number;
  };
}

export interface ContactResponse {
  messages: ContactMessage[];
  pagination: {
    currentPage: number;
    totalPages: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

class ContactService {
  // Submit contact form (public)
  async submitContactForm(formData: ContactFormData) {
    // Get CSRF token before submission
    await authService.getCsrfToken();
    return api.post('/contact/submit', formData);
  }

  // Get contact messages (admin only)
  async getContactMessages(
    page: number = 1,
    limit: number = 10,
    status: string = 'all',
    search: string = ''
  ) {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (status !== 'all') {
      params.append('status', status);
    }

    if (search) {
      params.append('search', search);
    }

    return api.get(`/contact?${params.toString()}`);
  }

  // Get single contact message (admin only)
  async getContactMessage(id: string) {
    return api.get(`/contact/${id}`);
  }

  // Update contact message status (admin only)
  async updateContactStatus(id: string, status: string, notes?: string) {
    await authService.getCsrfToken();
    return api.put(`/contact/${id}/status`, { status, notes });
  }

  // Delete contact message (admin only)
  async deleteContactMessage(id: string) {
    await authService.getCsrfToken();
    return api.delete(`/contact/${id}`);
  }

  // Get contact statistics (admin only)
  async getContactStats() {
    return api.get('/contact/stats');
  }
}

const contactService = new ContactService();
export default contactService;
```

#### Admin Dashboard (`src/components/dashboard/DashboardContacts.tsx`)

**Complete Admin Dashboard Implementation:**

```typescript
import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Eye, Trash2, Mail, Calendar, Clock, User } from "lucide-react";
import contactService, { ContactMessage } from "@/services/contact.service";
import { formatDistanceToNow } from "date-fns";

const DashboardContacts = () => {
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState("all");
  const [search, setSearch] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch contact messages
  const { data: contactData, isLoading } = useQuery({
    queryKey: ['contact-messages', page, status, search],
    queryFn: async () => {
      const response = await contactService.getContactMessages(page, 10, status, search);
      return response.data;
    },
  });

  // Fetch contact statistics
  const { data: statsData } = useQuery({
    queryKey: ['contact-stats'],
    queryFn: async () => {
      const response = await contactService.getContactStats();
      return response.data;
    },
  });

  // Handle status update
  const handleStatusUpdate = async (messageId: string, newStatus: string, notes?: string) => {
    try {
      await contactService.updateContactStatus(messageId, newStatus, notes);

      toast({
        title: "Status Updated",
        description: "Message status has been updated successfully.",
      });

      // Refresh data
      queryClient.invalidateQueries({ queryKey: ['contact-messages'] });
      queryClient.invalidateQueries({ queryKey: ['contact-stats'] });

      if (selectedMessage && selectedMessage._id === messageId) {
        setSelectedMessage({ ...selectedMessage, status: newStatus as any, notes });
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.response?.data?.message || "Failed to update status.",
      });
    }
  };

  // Handle message deletion
  const handleDelete = async (messageId: string) => {
    if (!confirm("Are you sure you want to delete this message?")) return;

    try {
      await contactService.deleteContactMessage(messageId);

      toast({
        title: "Message Deleted",
        description: "Contact message has been deleted successfully.",
      });

      // Refresh data
      queryClient.invalidateQueries({ queryKey: ['contact-messages'] });
      queryClient.invalidateQueries({ queryKey: ['contact-stats'] });

      if (selectedMessage && selectedMessage._id === messageId) {
        setIsDetailOpen(false);
        setSelectedMessage(null);
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.response?.data?.message || "Failed to delete message.",
      });
    }
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return <Badge variant="destructive">New</Badge>;
      case 'read':
        return <Badge variant="secondary">Read</Badge>;
      case 'replied':
        return <Badge variant="default">Replied</Badge>;
      case 'archived':
        return <Badge variant="outline">Archived</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Handle search with debouncing
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Contact Messages</h2>
      </div>

      {/* Statistics Cards */}
      {statsData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statsData.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statsData.today}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Messages</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statsData.byStatus.new || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Replied</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statsData.byStatus.replied || 0}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder="Search messages..."
          value={search}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="max-w-sm"
        />
        <Select value={status} onValueChange={(value) => { setStatus(value); setPage(1); }}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Messages</SelectItem>
            <SelectItem value="new">New</SelectItem>
            <SelectItem value="read">Read</SelectItem>
            <SelectItem value="replied">Replied</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>Messages</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-4">Loading messages...</div>
          ) : contactData?.messages?.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">No messages found.</div>
          ) : (
            <div className="space-y-4">
              {contactData?.messages?.map((message: ContactMessage) => (
                <div key={message._id} className="border rounded-lg p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-semibold">{message.name}</h4>
                        {getStatusBadge(message.status)}
                      </div>
                      <p className="text-sm text-muted-foreground">{message.email}</p>
                      <p className="text-sm">{message.message.substring(0, 100)}...</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedMessage(message);
                          setIsDetailOpen(true);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Select
                        value={message.status}
                        onValueChange={(value) => handleStatusUpdate(message._id, value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="read">Read</SelectItem>
                          <SelectItem value="replied">Replied</SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(message._id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {contactData?.pagination && contactData.pagination.totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={!contactData.pagination.hasPrev}
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {contactData.pagination.currentPage} of {contactData.pagination.totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={!contactData.pagination.hasNext}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Message Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Message Details</DialogTitle>
          </DialogHeader>
          {selectedMessage && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Name</Label>
                  <p className="text-sm">{selectedMessage.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-sm">{selectedMessage.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <div className="mt-1">{getStatusBadge(selectedMessage.status)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Received</Label>
                  <p className="text-sm">
                    {formatDistanceToNow(new Date(selectedMessage.createdAt), { addSuffix: true })}
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Message</Label>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">{selectedMessage.message}</p>
              </div>

              {selectedMessage.notes && (
                <div>
                  <Label className="text-sm font-medium">Notes</Label>
                  <p className="text-sm mt-1 p-3 bg-muted rounded-md">{selectedMessage.notes}</p>
                </div>
              )}

              {selectedMessage.repliedBy && (
                <div>
                  <Label className="text-sm font-medium">Replied By</Label>
                  <p className="text-sm">
                    {selectedMessage.repliedBy.firstName} {selectedMessage.repliedBy.lastName}
                    ({selectedMessage.repliedBy.username})
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {selectedMessage.repliedAt &&
                      formatDistanceToNow(new Date(selectedMessage.repliedAt), { addSuffix: true })
                    }
                  </p>
                </div>
              )}

              <div className="flex gap-2">
                <Select
                  value={selectedMessage.status}
                  onValueChange={(value) => handleStatusUpdate(selectedMessage._id, value)}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                    <SelectItem value="replied">Replied</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="destructive"
                  onClick={() => handleDelete(selectedMessage._id)}
                >
                  Delete Message
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DashboardContacts;
```

**Status Workflow:**
1. **New**: Freshly submitted messages (red badge)
2. **Read**: Messages that have been viewed by admin (gray badge)
3. **Replied**: Messages that have received a response (blue badge)
4. **Archived**: Completed or closed messages (outline badge)

## Security Features

### Input Validation
- **Server-Side Validation**: Comprehensive validation on all inputs
- **Length Limits**: Prevents oversized submissions
- **Email Validation**: Regex-based email format checking
- **XSS Prevention**: Input sanitization and escaping

### CSRF Protection

**Complete CSRF Middleware Implementation (`backend/middleware/csrf.middleware.js`):**

```javascript
import csrf from 'csurf';
import { randomBytes } from 'crypto';

// Configure CSRF protection
const csrfMiddleware = csrf({
  cookie: {
    httpOnly: true,
    secure: false, // Set to false to work in both HTTP and HTTPS
    sameSite: 'lax' // Changed from strict to lax for better compatibility
  }
});

// Create a wrapper middleware that logs CSRF information
const csrfProtection = (req, res, next) => {
  // Add debug logging for troubleshooting
  console.log('CSRF Protection Middleware Called');
  console.log('CSRF Cookie:', req.cookies['_csrf']);
  console.log('CSRF Header:', req.headers['x-csrf-token']);
  console.log('All Cookies:', req.cookies);

  // Pass to the actual CSRF middleware
  csrfMiddleware(req, res, (err) => {
    if (err) {
      console.error('CSRF Middleware Error:', err);
      // Send more detailed error response
      if (err.code === 'EBADCSRFTOKEN') {
        return res.status(403).json({
          message: 'Invalid CSRF token. Please refresh the page and try again.',
          error: err.message,
          code: err.code
        });
      }
      return next(err);
    }

    // Log the generated token for debugging
    console.log('Generated CSRF Token:', req.csrfToken());
    next();
  });
};

// Middleware to handle CSRF errors gracefully
const handleCSRFError = (err, req, res, next) => {
  if (err.code !== 'EBADCSRFTOKEN') {
    return next(err);
  }

  // More debug information when CSRF validation fails
  console.log('CSRF Error - Cookies:', req.cookies);
  console.log('CSRF Error - Headers:', req.headers);

  // Handle CSRF token errors
  res.status(403).json({
    message: 'Invalid or missing CSRF token. Please refresh the page and try again.',
    error: err.message,
    code: err.code
  });
};

export { csrfProtection, handleCSRFError };
```

**CSRF Token Handling Features:**
- **Token Validation**: All form submissions require valid CSRF tokens
- **Automatic Handling**: Frontend automatically manages tokens via auth service
- **Error Recovery**: Graceful handling of token failures with detailed error messages
- **Debug Logging**: Comprehensive logging for troubleshooting CSRF issues
- **Cookie Configuration**: Optimized cookie settings for development and production

### Rate Limiting
- **IP-Based Tracking**: Monitors submission frequency per IP
- **Spam Prevention**: Prevents automated form submissions
- **User Agent Logging**: Tracks submission sources

## API Reference

### Submit Contact Form

```http
POST /api/contact/submit
Content-Type: application/json
X-CSRF-Token: <csrf-token>

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "I love your coffee! When are you open on Sundays?"
}
```

**Response:**
```json
{
  "message": "Thank you for your message! We'll get back to you soon.",
  "id": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

### Get Contact Messages (Admin)

```http
GET /api/contact?page=1&limit=10&status=new&search=coffee
Authorization: Bearer <admin-token>
```

**Response:**
```json
{
  "messages": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "name": "John Doe",
      "email": "<EMAIL>",
      "message": "I love your coffee!",
      "status": "new",
      "ipAddress": "***********",
      "replied": false,
      "createdAt": "2023-07-21T10:30:00.000Z",
      "updatedAt": "2023-07-21T10:30:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "total": 50,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Update Message Status (Admin)

```http
PUT /api/contact/:id/status
Authorization: Bearer <admin-token>
X-CSRF-Token: <csrf-token>
Content-Type: application/json

{
  "status": "replied",
  "notes": "Responded via email on 2023-07-21"
}
```

## Usage Guide

### For Customers

1. **Access Contact Form**:
   - Navigate to `/contact` page
   - Fill in name, email, and message
   - Submit form

2. **Form Validation**:
   - Name: Minimum 2 characters
   - Email: Valid email format required
   - Message: Minimum 10 characters

3. **Submission Feedback**:
   - Success: Green toast notification
   - Error: Red toast with specific error message

### For Administrators

1. **Access Contact Dashboard**:
   - Login as admin user
   - Navigate to Dashboard → Contact Messages

2. **View Messages**:
   - Browse paginated message list
   - Use search to find specific messages
   - Filter by status (new, read, replied, archived)

3. **Manage Messages**:
   - Click eye icon to view full message details
   - Use status dropdown to update message status
   - Add internal notes for tracking
   - Delete messages when necessary

4. **Monitor Statistics**:
   - View total message count
   - Track today's submissions
   - Monitor new vs. replied ratios
   - Analyze message trends

## Performance Considerations

### Database Optimization
- **Indexes**: Optimized queries with proper indexing
- **Pagination**: Efficient handling of large datasets
- **Aggregation**: Statistics calculated via MongoDB aggregation

### Frontend Performance
- **React Query**: Caching and background updates
- **Lazy Loading**: Components loaded on demand
- **Debounced Search**: Prevents excessive API calls
- **Optimistic Updates**: Immediate UI feedback

## Monitoring and Analytics

### Message Tracking
- **Submission Metrics**: Track daily/weekly submission volumes
- **Response Times**: Monitor time from submission to reply
- **Status Distribution**: Analyze message workflow efficiency
- **Source Analysis**: IP and user agent tracking

### Admin Activity
- **User Tracking**: Monitor which admins handle messages
- **Response Patterns**: Analyze admin response efficiency
- **Status Changes**: Track message lifecycle progression

## Troubleshooting

### Common Issues

#### Form Submission Failures
- **CSRF Token Issues**: Ensure tokens are properly generated and included
- **Validation Errors**: Check input format and length requirements
- **Network Issues**: Verify API connectivity and CORS settings

#### Admin Dashboard Problems
- **Authentication**: Ensure admin user is properly logged in
- **Permission Issues**: Verify admin role assignment
- **Loading Issues**: Check API endpoints and data formatting

### Debug Commands

```bash
# Test contact form submission
curl -X POST http://localhost:5000/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: YOUR_TOKEN" \
  -d '{"name":"Test","email":"<EMAIL>","message":"Test message"}'

# Check contact messages in database
mongo l-cafe --eval "db.contacts.find().pretty()"

# View contact statistics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:5000/api/contact/stats
```

## Future Enhancements

### Planned Features
- **Email Notifications**: Automatic email alerts for new messages
- **Response Templates**: Pre-defined response templates
- **Auto-Reply**: Automatic acknowledgment emails
- **Message Categories**: Categorize messages by topic
- **Priority Levels**: Mark urgent messages
- **Export Functionality**: Export messages to CSV/PDF

### Integration Possibilities
- **CRM Integration**: Connect with customer relationship management systems
- **Help Desk**: Integration with support ticket systems
- **Analytics**: Advanced reporting and analytics dashboard
- **Mobile App**: Mobile admin interface for message management

## Conclusion

The Contact Form system provides a complete solution for customer communication management in L Café. It combines user-friendly public interfaces with powerful admin tools, ensuring efficient handling of customer inquiries while maintaining security and performance standards.

Key strengths:
- **Complete Workflow**: From submission to resolution tracking
- **Security-First**: CSRF protection and input validation
- **Admin-Friendly**: Intuitive management interface
- **Scalable**: Efficient handling of high message volumes
- **Analytics-Ready**: Comprehensive tracking and reporting
