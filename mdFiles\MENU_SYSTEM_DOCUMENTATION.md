# Menu System Documentation

## Overview

The Menu System is a comprehensive feature of the L Café application that allows administrators to create, manage, and display menu items to customers. The system supports categorization, dietary information, featured items, seasonal offerings, and automatic sitemap integration.

## Architecture

### Backend Components

#### 1. Database Model (`backend/models/menu.model.js`)

The menu item model defines the structure for storing menu items in MongoDB:

```javascript
const menuItemSchema = new mongoose.Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String },
  category: { 
    type: String, 
    enum: ['coffee', 'tea', 'food', 'dessert', 'seasonal'],
    default: 'coffee'
  },
  price: { type: Number, required: true },
  image: { type: String },
  dietaryInfo: {
    vegan: { type: Boolean, default: false },
    glutenFree: { type: Boolean, default: false },
    vegetarian: { type: Boolean, default: false }
  },
  featured: { type: Boolean, default: false },
  seasonal: { type: Boolean, default: false },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Key Features:**
- **Automatic Slug Generation**: SEO-friendly URLs with pre-save middleware
- **Category System**: Five predefined categories (coffee, tea, food, dessert, seasonal)
- **Dietary Information**: Vegan, gluten-free, and vegetarian flags
- **Featured System**: Ability to mark items as featured for homepage display
- **Seasonal Items**: Special marking for seasonal or limited-time offerings
- **Soft Delete**: Uses `active` field for safe deletion
- **Price Management**: Numeric price field for accurate calculations

#### 2. Pre-Save Middleware

The model includes automatic slug generation:

```javascript
menuItemSchema.pre('save', async function(next) {
  if (this.isModified('name')) {
    let slug = slugify(this.name, {
      lower: true,
      strict: true,
      locale: 'tr'
    });
    
    const slugExists = await this.constructor.findOne({ 
      slug, 
      _id: { $ne: this._id } 
    });
    
    if (slugExists) {
      slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
    }
    
    this.slug = slug;
  }
  
  this.updatedAt = Date.now();
  next();
});
```

#### 3. Controller (`backend/controllers/menu.controller.js`)

**Complete Controller Implementation:**

```javascript
// controllers/menu.controller.js
import MenuItem from '../models/menu.model.js';
import mongoose from 'mongoose';
import slugify from 'slugify';
import { autoUpdateSitemap } from './sitemap.controller.js';

// Get all menu items with pagination and filtering
export const getMenuItems = async (req, res) => {
  try {
    const { page = 1, limit = 10, category, featured, seasonal, active = 'true' } = req.query;

    // Build filter object
    const filter = { active: active === 'true' };

    if (category) filter.category = category;
    if (featured !== undefined) filter.featured = featured === 'true';
    if (seasonal !== undefined) filter.seasonal = seasonal === 'true';

    // Calculate how many documents to skip
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get menu items with pagination
    const menuItems = await MenuItem.find(filter)
      .sort({ category: 1, name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await MenuItem.countDocuments(filter);

    res.status(200).json({
      menuItems,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching menu items',
      error: error.message
    });
  }
};

// Get menu categories
export const getMenuCategories = async (req, res) => {
  try {
    const categories = await MenuItem.distinct('category');
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching menu categories',
      error: error.message
    });
  }
};

// Get menu items by category
export const getMenuItemsByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 10 } = req.query;

    // Calculate how many documents to skip
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get menu items with pagination
    const menuItems = await MenuItem.find({ category, active: true })
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await MenuItem.countDocuments({ category, active: true });

    res.status(200).json({
      menuItems,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching menu items by category',
      error: error.message
    });
  }
};

// Get menu item by slug
export const getMenuItemBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    const menuItem = await MenuItem.findOne({ slug, active: true });

    if (!menuItem) {
      return res.status(404).json({ message: 'Menu item not found' });
    }

    res.status(200).json(menuItem);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching menu item',
      error: error.message
    });
  }
};

// Get featured menu items
export const getFeaturedMenuItems = async (req, res) => {
  try {
    const featuredItems = await MenuItem.find({ featured: true, active: true })
      .sort({ category: 1, name: 1 });

    res.status(200).json(featuredItems);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching featured menu items',
      error: error.message
    });
  }
};

// Get seasonal menu items
export const getSeasonalMenuItems = async (req, res) => {
  try {
    const seasonalItems = await MenuItem.find({ seasonal: true, active: true })
      .sort({ category: 1, name: 1 });

    res.status(200).json(seasonalItems);
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching seasonal menu items',
      error: error.message
    });
  }
};

// Create a new menu item
export const createMenuItem = async (req, res) => {
  try {
    const {
      name, description, category, price,
      image, dietaryInfo, featured, seasonal
    } = req.body;

    // Validation
    if (!name || !price) {
      return res.status(400).json({ message: 'Name and price are required' });
    }

    // Create slug from name
    let slug = slugify(name, {
      lower: true,
      strict: true,
      locale: 'tr'
    });

    // Check if slug exists
    const slugExists = await MenuItem.findOne({ slug });

    // If slug exists, append a random string
    if (slugExists) {
      slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
    }

    // Create new menu item
    const menuItem = new MenuItem({
      name,
      slug,
      description,
      category: category || 'coffee',
      price: parseFloat(price),
      image,
      dietaryInfo: dietaryInfo || {
        vegan: false,
        glutenFree: false,
        vegetarian: false
      },
      featured: featured === 'true',
      seasonal: seasonal === 'true',
      active: true
    });

    // Save to database
    await menuItem.save();

    // Auto-update sitemap
    await autoUpdateSitemap('menu', 'create', {
      id: menuItem._id,
      name: menuItem.name
    });

    res.status(201).json({
      message: 'Menu item created successfully',
      menuItem
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error creating menu item',
      error: error.message
    });
  }
};

// Update an existing menu item
export const updateMenuItem = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name, description, category, price,
      image, dietaryInfo, featured, seasonal, active
    } = req.body;

    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid menu item ID' });
    }

    // Find menu item
    const menuItem = await MenuItem.findById(id);

    if (!menuItem) {
      return res.status(404).json({ message: 'Menu item not found' });
    }

    // Check if name is changed, then update slug
    let slug = menuItem.slug;
    if (name && name !== menuItem.name) {
      slug = slugify(name, {
        lower: true,
        strict: true,
        locale: 'tr'
      });

      // Check if new slug exists
      const slugExists = await MenuItem.findOne({
        slug,
        _id: { $ne: id }
      });

      if (slugExists) {
        slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
      }
    }

    // Update menu item
    const updatedMenuItem = await MenuItem.findByIdAndUpdate(
      id,
      {
        name: name || menuItem.name,
        slug,
        description: description !== undefined ? description : menuItem.description,
        category: category || menuItem.category,
        price: price !== undefined ? parseFloat(price) : menuItem.price,
        image: image !== undefined ? image : menuItem.image,
        dietaryInfo: dietaryInfo || menuItem.dietaryInfo,
        featured: featured !== undefined ? featured === 'true' : menuItem.featured,
        seasonal: seasonal !== undefined ? seasonal === 'true' : menuItem.seasonal,
        active: active !== undefined ? active === 'true' : menuItem.active,
        updatedAt: Date.now()
      },
      { new: true }
    );

    // Auto-update sitemap if slug changed
    if (slug !== menuItem.slug) {
      await autoUpdateSitemap('menu', 'update', {
        id: updatedMenuItem._id,
        oldSlug: menuItem.slug,
        newSlug: slug,
        name: updatedMenuItem.name
      });
    }

    res.status(200).json({
      message: 'Menu item updated successfully',
      menuItem: updatedMenuItem
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error updating menu item',
      error: error.message
    });
  }
};

// Delete a menu item (soft delete)
export const deleteMenuItem = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid menu item ID' });
    }

    // Soft delete (set active to false)
    const menuItem = await MenuItem.findByIdAndUpdate(
      id,
      { active: false, updatedAt: Date.now() },
      { new: true }
    );

    if (!menuItem) {
      return res.status(404).json({ message: 'Menu item not found' });
    }

    // Auto-update sitemap
    await autoUpdateSitemap('menu', 'delete', {
      id: menuItem._id
    });

    res.status(200).json({
      message: 'Menu item deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error deleting menu item',
      error: error.message
    });
  }
};

// Hard delete a menu item (admin only)
export const hardDeleteMenuItem = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate object ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid menu item ID' });
    }

    // Permanently remove from database
    const result = await MenuItem.findByIdAndDelete(id);

    if (!result) {
      return res.status(404).json({ message: 'Menu item not found' });
    }

    // Auto-update sitemap
    await autoUpdateSitemap('menu', 'delete', {
      id: result._id
    });

    res.status(200).json({
      message: 'Menu item permanently deleted'
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error deleting menu item',
      error: error.message
    });
  }
};
```

#### 4. Routes (`backend/routes/menu.routes.js`)

The routing system organizes endpoints by access level:

**Public Routes (No Authentication):**
- `GET /api/menu` - List menu items with pagination and filtering
- `GET /api/menu/categories` - Get all available categories
- `GET /api/menu/category/:category` - Get items by category
- `GET /api/menu/featured` - Get featured menu items
- `GET /api/menu/seasonal` - Get seasonal menu items
- `GET /api/menu/:slug` - Get single menu item by slug

**Protected Routes (Authentication + CSRF):**
- `POST /api/menu` - Create new menu item
- `PUT /api/menu/:id` - Update menu item
- `DELETE /api/menu/:id` - Soft delete menu item

**Admin-Only Routes:**
- `DELETE /api/menu/:id/permanent` - Hard delete menu item

#### 5. Middleware Integration

**Security Middleware:**
- **Authentication**: `authMiddleware` validates JWT tokens
- **CSRF Protection**: `csrfProtection` prevents cross-site request forgery
- **Role-Based Access**: `roleMiddleware(['admin'])` for admin-only operations

**Automatic Features:**
- **Sitemap Integration**: Automatically updates sitemap on CRUD operations
- **Slug Generation**: Automatic URL-friendly identifier creation

### Frontend Components

#### 1. Service Layer (`src/services/menu.service.ts`)

The service layer handles API communication:

```typescript
interface MenuItem {
  _id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  featured: boolean;
  seasonal: boolean;
  dietaryInfo: {
    vegan: boolean;
    glutenFree: boolean;
    vegetarian: boolean;
  };
}

interface CreateMenuItemData {
  name: string;
  description: string;
  category: string;
  price: number;
  image?: string;
  featured?: boolean;
  seasonal?: boolean;
  dietaryInfo?: {
    vegan: boolean;
    glutenFree: boolean;
    vegetarian: boolean;
  };
}
```

**Service Methods:**
- `getAllItems(page, limit)` - Fetch paginated menu items
- `getItemsByCategory(category, page, limit)` - Fetch items by category
- `getCategories()` - Fetch available categories
- `getFeaturedItems()` - Fetch featured menu items
- `getSeasonalItems()` - Fetch seasonal menu items
- `getItemBySlug(slug)` - Fetch single menu item
- `createMenuItem(data)` - Create new menu item
- `updateMenuItem(id, data)` - Update existing menu item
- `deleteMenuItem(id)` - Delete menu item

**CSRF Token Handling:**
All write operations automatically ensure CSRF tokens are available before making requests.

#### 2. Dashboard Management (`src/components/dashboard/DashboardMenu.tsx`)

The admin dashboard provides full CRUD functionality:

**Features:**
- **Table View**: Displays menu items in a comprehensive table
- **Pagination**: Handles large datasets efficiently
- **Create Dialog**: Form for creating new menu items
- **Edit Dialog**: Form for updating existing menu items
- **Delete Confirmation**: Safety dialog for deletion operations
- **Real-time Updates**: Uses React Query for cache invalidation

**Table Columns:**
- Name
- Category
- Price (formatted as currency)
- Featured status
- Seasonal status
- Dietary information badges
- Action buttons (Edit/Delete)

#### 3. Menu Item Form (`src/components/dashboard/MenuItemForm.tsx`)

Comprehensive form component for creating and editing menu items:

**Form Fields:**
- **Name** (required) - Text input
- **Description** - Textarea
- **Category** - Dropdown with dynamic categories
- **Price** - Number input with decimal support
- **Dietary Information** - Checkboxes for vegan, gluten-free, vegetarian
- **Featured** - Checkbox with description
- **Seasonal** - Checkbox with description

**Features:**
- **Dynamic Categories**: Fetches categories from API
- **Form Validation**: Client-side validation with react-hook-form
- **Loading States**: Visual feedback during submission
- **Error Handling**: Toast notifications for errors

#### 4. Delete Confirmation (`src/components/dashboard/DeleteMenuItemDialog.tsx`)

Safety dialog for menu item deletion:

**Features:**
- **Confirmation Dialog**: Prevents accidental deletions
- **Loading State**: Shows progress during deletion
- **Error Handling**: Toast notifications for errors
- **CSRF Protection**: Automatic token handling

#### 5. Public Display Components

**Homepage Menu Component (`src/components/Menu.tsx`):**
- Displays featured menu items on homepage
- Groups items by category
- Responsive card-based layout
- Shows dietary information badges
- Price formatting
- Shows "Menu is Empty" message when no featured items are available
- No fallback to mock data - always uses real API data

**Full Menu Page (`src/pages/Menu.tsx`):**
- Tabbed interface for category filtering
- Shows all menu items with pagination
- Category-based filtering (All, Featured, Seasonal, Coffee, Tea, Food, Dessert)
- Card-based responsive layout
- Dietary information badges
- Featured and seasonal badges

## API Endpoints

### Public Endpoints

#### GET /api/menu
Retrieve menu items with optional filtering and pagination.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10) - Items per page
- `category` (string) - Filter by category
- `featured` (boolean) - Filter featured items
- `seasonal` (boolean) - Filter seasonal items
- `active` (boolean, default: true) - Filter active items

**Response:**
```json
{
  "menuItems": [...],
  "totalPages": 5,
  "currentPage": 1,
  "total": 47
}
```

#### GET /api/menu/categories
Retrieve all available menu categories.

**Response:**
```json
["coffee", "tea", "food", "dessert", "seasonal"]
```

#### GET /api/menu/category/:category
Retrieve menu items by specific category.

**Parameters:**
- `category` (string) - Category name

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10) - Items per page

**Response:**
```json
{
  "menuItems": [...],
  "totalPages": 3,
  "currentPage": 1,
  "total": 25
}
```

#### GET /api/menu/featured
Retrieve all featured menu items.

**Response:**
```json
[
  {
    "_id": "...",
    "name": "House Blend Coffee",
    "slug": "house-blend-coffee",
    "description": "Our signature medium roast...",
    "category": "coffee",
    "price": 4.50,
    "featured": true,
    "seasonal": false,
    "dietaryInfo": {
      "vegan": false,
      "glutenFree": true,
      "vegetarian": true
    }
  }
]
```

#### GET /api/menu/seasonal
Retrieve all seasonal menu items.

**Response:**
```json
[
  {
    "_id": "...",
    "name": "Pumpkin Spice Latte",
    "slug": "pumpkin-spice-latte",
    "description": "Seasonal favorite with real pumpkin...",
    "category": "coffee",
    "price": 5.75,
    "featured": false,
    "seasonal": true,
    "dietaryInfo": {
      "vegan": false,
      "glutenFree": false,
      "vegetarian": true
    }
  }
]
```

#### GET /api/menu/:slug
Retrieve a single menu item by slug.

**Parameters:**
- `slug` (string) - Menu item slug

**Response:**
```json
{
  "_id": "...",
  "name": "Cappuccino",
  "slug": "cappuccino",
  "description": "Espresso with steamed milk and foam",
  "category": "coffee",
  "price": 5.00,
  "image": "https://example.com/cappuccino.jpg",
  "featured": true,
  "seasonal": false,
  "dietaryInfo": {
    "vegan": false,
    "glutenFree": true,
    "vegetarian": true
  },
  "active": true,
  "createdAt": "2024-01-15T10:00:00.000Z",
  "updatedAt": "2024-01-15T10:00:00.000Z"
}
```

### Protected Endpoints

#### POST /api/menu
Create a new menu item.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Request Body:**
```json
{
  "name": "New Coffee Blend",
  "description": "A unique blend of premium beans",
  "category": "coffee",
  "price": 6.25,
  "image": "optional-image-url",
  "featured": true,
  "seasonal": false,
  "dietaryInfo": {
    "vegan": false,
    "glutenFree": true,
    "vegetarian": true
  }
}
```

**Response:**
```json
{
  "message": "Menu item created successfully",
  "menuItem": { ... }
}
```

#### PUT /api/menu/:id
Update an existing menu item.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Parameters:**
- `id` (string) - Menu item ID

**Request Body:** (partial update supported)
```json
{
  "name": "Updated Coffee Name",
  "price": 7.00,
  "featured": false
}
```

**Response:**
```json
{
  "message": "Menu item updated successfully",
  "menuItem": { ... }
}
```

#### DELETE /api/menu/:id
Soft delete a menu item.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Parameters:**
- `id` (string) - Menu item ID

**Response:**
```json
{
  "message": "Menu item deleted successfully"
}
```

### Admin-Only Endpoints

#### DELETE /api/menu/:id/permanent
Permanently delete a menu item (hard delete).

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Parameters:**
- `id` (string) - Menu item ID

**Response:**
```json
{
  "message": "Menu item permanently deleted"
}
```

## Security Features

### Authentication & Authorization
- **JWT Authentication**: All write operations require valid JWT tokens
- **Role-Based Access**: Admin-only operations are protected
- **CSRF Protection**: All state-changing operations require CSRF tokens

### Data Validation
- **Input Validation**: Required fields are validated on both frontend and backend
- **MongoDB ObjectId Validation**: Ensures valid IDs for operations
- **Price Validation**: Ensures numeric values for pricing
- **Category Validation**: Restricts to predefined categories
- **Slug Uniqueness**: Automatic handling of duplicate slugs

### Soft Delete Pattern
- Menu items are never permanently deleted by default
- Uses `active: false` for soft deletion
- Admin users can perform hard deletes when necessary

## Integration Features

### Sitemap Integration
The menu system automatically integrates with the sitemap system:

- **Create**: Adds new menu item URLs to sitemap
- **Update**: Updates existing entries if slug changes
- **Delete**: Removes URLs from sitemap

### Automatic Slug Generation
- Uses `slugify` library with Turkish character support
- Ensures uniqueness by appending random numbers
- SEO-friendly URLs for better search engine optimization

## Error Handling

### Backend Error Responses
All endpoints return consistent error responses:

```json
{
  "message": "Error description",
  "error": "Detailed error message (development only)"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions or CSRF error)
- `404` - Not Found
- `500` - Internal Server Error

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Visual feedback during operations
- **Retry Logic**: Automatic CSRF token refresh on failures
- **Form Validation**: Client-side validation with helpful messages

## Performance Considerations

### Database Optimization
- **Indexing**: Unique index on slug field for fast lookups
- **Pagination**: Efficient skip/limit queries for large datasets
- **Category Filtering**: Optimized queries for category-based filtering
- **Sorting**: Consistent sorting by category and name

### Frontend Optimization
- **React Query**: Intelligent caching and background updates
- **Lazy Loading**: Components load only when needed
- **Debounced Searches**: Prevents excessive API calls
- **Memoization**: Optimized re-renders for large lists

## Deployment Notes

### Environment Variables
No specific environment variables required for menu system.

### Database Migrations
The menu collection is created automatically when the first menu item is saved.

### Dependencies
**Backend:**
- `mongoose` - MongoDB ODM
- `slugify` - URL-friendly slug generation
- `express` - Web framework
- `jsonwebtoken` - JWT authentication
- `csurf` - CSRF protection

**Frontend:**
- `@tanstack/react-query` - Data fetching and caching
- `react-hook-form` - Form management
- `lucide-react` - Icons
- `@radix-ui/react-*` - UI components

## Testing Recommendations

### Backend Testing
1. **Unit Tests**: Test individual controller functions
2. **Integration Tests**: Test complete API endpoints
3. **Authentication Tests**: Verify security middleware
4. **Database Tests**: Test model validations and constraints
5. **Slug Generation Tests**: Test uniqueness and formatting

### Frontend Testing
1. **Component Tests**: Test individual React components
2. **Service Tests**: Test API service functions
3. **Integration Tests**: Test complete user workflows
4. **Form Tests**: Test form validation and submission
5. **Accessibility Tests**: Ensure WCAG compliance

## Troubleshooting

### Common Issues

1. **CSRF Token Errors**
   - Ensure frontend is properly fetching and sending CSRF tokens
   - Check cookie settings in CSRF middleware

2. **Slug Conflicts**
   - The system automatically handles conflicts by appending random numbers
   - Check `createUniqueSlug` function for custom logic

3. **Authentication Failures**
   - Verify JWT token is valid and not expired
   - Check Authorization header format: `Bearer <token>`

4. **Price Formatting Issues**
   - Ensure price is sent as a number, not string
   - Check parseFloat conversion in controller

5. **Category Validation Errors**
   - Verify category is one of the allowed enum values
   - Check category dropdown options match backend enum

6. **Homepage Showing Mock Data**
   - Ensure the homepage Menu component is fetching from API, not using hardcoded data
   - Check that the component shows "Menu is Empty" when no featured items exist
   - Verify featured items are properly marked in the database

### Debugging Tips

1. **Enable Debug Logging**: Check console logs for detailed error information
2. **Database Queries**: Use MongoDB Compass to inspect data directly
3. **Network Tab**: Monitor API requests and responses in browser dev tools
4. **React Query DevTools**: Use for debugging cache and query states
5. **Form State**: Use React Hook Form DevTools for form debugging

## Code Examples

### Backend Code Snippets

#### Menu Item Creation with Validation
```javascript
export const createMenuItem = async (req, res) => {
  try {
    const {
      name, description, category, price,
      image, dietaryInfo, featured, seasonal
    } = req.body;

    // Validation
    if (!name || !price) {
      return res.status(400).json({ message: 'Name and price are required' });
    }

    // Create slug from name
    let slug = slugify(name, {
      lower: true,
      strict: true,
      locale: 'tr'
    });

    // Check if slug exists
    const slugExists = await MenuItem.findOne({ slug });

    // If slug exists, append a random string
    if (slugExists) {
      slug = `${slug}-${Math.floor(Math.random() * 1000)}`;
    }

    // Create new menu item
    const menuItem = new MenuItem({
      name,
      slug,
      description,
      category: category || 'coffee',
      price: parseFloat(price),
      image,
      dietaryInfo: dietaryInfo || {
        vegan: false,
        glutenFree: false,
        vegetarian: false
      },
      featured: featured === 'true',
      seasonal: seasonal === 'true',
      active: true
    });

    // Save to database
    await menuItem.save();

    // Auto-update sitemap
    await autoUpdateSitemap('menu', 'create', {
      id: menuItem._id,
      name: menuItem.name
    });

    res.status(201).json({
      message: 'Menu item created successfully',
      menuItem
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error creating menu item',
      error: error.message
    });
  }
};
```

#### Advanced Filtering and Pagination
```javascript
export const getMenuItems = async (req, res) => {
  try {
    const { page = 1, limit = 10, category, featured, seasonal, active = 'true' } = req.query;

    // Build filter object
    const filter = { active: active === 'true' };

    if (category) filter.category = category;
    if (featured !== undefined) filter.featured = featured === 'true';
    if (seasonal !== undefined) filter.seasonal = seasonal === 'true';

    // Calculate how many documents to skip
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get menu items with pagination
    const menuItems = await MenuItem.find(filter)
      .sort({ category: 1, name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await MenuItem.countDocuments(filter);

    res.status(200).json({
      menuItems,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error fetching menu items',
      error: error.message
    });
  }
};
```

### Frontend Code Snippets

#### Service Layer Implementation
```typescript
const menuService = {
  getAllItems: async (page = 1, limit = 10) => {
    return api.get(`/menu?page=${page}&limit=${limit}`);
  },

  createMenuItem: async (menuItemData: CreateMenuItemData) => {
    try {
      await authService.getCsrfToken();
      console.log('Creating menu item with data:', menuItemData);

      return api.post('/menu', menuItemData);
    } catch (error) {
      console.error('Error in createMenuItem service:', error);
      throw error;
    }
  },

  updateMenuItem: async (id: string, menuItemData: Partial<CreateMenuItemData>) => {
    try {
      await authService.getCsrfToken();
      console.log('Updating menu item with data:', menuItemData);

      return api.put(`/menu/${id}`, menuItemData);
    } catch (error) {
      console.error('Error in updateMenuItem service:', error);
      throw error;
    }
  },

  deleteMenuItem: async (id: string) => {
    try {
      await authService.getCsrfToken();
      console.log('Deleting menu item with id:', id);

      return api.delete(`/menu/${id}`);
    } catch (error) {
      console.error('Error in deleteMenuItem service:', error);
      throw error;
    }
  }
};
```

#### React Component with Query Management
```typescript
const DashboardMenu = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['menu-items', currentPage],
    queryFn: async () => {
      const response = await menuService.getAllItems(currentPage, 10);
      return response.data;
    },
  });

  const handleCreateSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['menu-items'] });
    queryClient.invalidateQueries({ queryKey: ['menu-categories'] });
    toast({
      title: "Success",
      description: "Menu item created successfully",
    });
  };

  const handleDeleteSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['menu-items'] });
    toast({
      title: "Success",
      description: "Menu item deleted successfully",
    });
  };

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};
```

#### Form Component with Validation
```typescript
const MenuItemForm = ({ initialData, onSuccess, onCancel }: MenuItemFormProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreateMenuItemData>({
    defaultValues: initialData ? {
      name: initialData.name,
      description: initialData.description,
      category: initialData.category,
      price: initialData.price,
      featured: initialData.featured,
      seasonal: initialData.seasonal,
      dietaryInfo: initialData.dietaryInfo
    } : {
      name: "",
      description: "",
      category: "coffee",
      price: 0,
      featured: false,
      seasonal: false,
      dietaryInfo: {
        vegan: false,
        glutenFree: false,
        vegetarian: false
      }
    }
  });

  const handleSubmit = async (data: CreateMenuItemData) => {
    try {
      setIsSubmitting(true);

      if (initialData) {
        await menuService.updateMenuItem(initialData._id, data);
        toast({
          title: "Menu item updated",
          description: `${data.name} has been updated successfully.`
        });
      } else {
        await menuService.createMenuItem(data);
        toast({
          title: "Menu item created",
          description: `${data.name} has been added to the menu.`
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Error submitting menu item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem saving the menu item."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Form fields */}
      </form>
    </Form>
  );
};
```

## Database Schema Details

### Menu Item Collection Structure
```javascript
{
  _id: ObjectId("..."),
  name: "Cappuccino",
  slug: "cappuccino",
  description: "Espresso with steamed milk and delicate foam",
  category: "coffee", // enum: ['coffee', 'tea', 'food', 'dessert', 'seasonal']
  price: 5.00,
  image: "https://example.com/cappuccino.jpg", // optional
  dietaryInfo: {
    vegan: false,
    glutenFree: true,
    vegetarian: true
  },
  featured: true,
  seasonal: false,
  active: true,
  createdAt: ISODate("2024-01-15T10:00:00.000Z"),
  updatedAt: ISODate("2024-01-15T10:00:00.000Z")
}
```

### Indexes
```javascript
// Unique index on slug for fast lookups and uniqueness
db.menuitems.createIndex({ "slug": 1 }, { unique: true })

// Compound index for efficient filtering and sorting
db.menuitems.createIndex({ "active": 1, "category": 1, "featured": 1, "name": 1 })

// Index on category for category-based filtering
db.menuitems.createIndex({ "category": 1 })

// Index for featured items
db.menuitems.createIndex({ "featured": 1, "active": 1 })

// Index for seasonal items
db.menuitems.createIndex({ "seasonal": 1, "active": 1 })
```

## File Structure

### Backend Files
```
backend/
├── models/
│   └── menu.model.js                 # MongoDB schema definition with pre-save hooks
├── controllers/
│   └── menu.controller.js            # Business logic and API handlers
├── routes/
│   └── menu.routes.js                # Route definitions and middleware
└── middleware/
    ├── auth.middleware.js            # JWT authentication
    ├── csrf.middleware.js            # CSRF protection
    └── role.middleware.js            # Role-based access control
```

### Frontend Files
```
src/
├── services/
│   └── menu.service.ts               # API service layer with TypeScript interfaces
├── components/
│   ├── Menu.tsx                      # Homepage featured menu display
│   └── dashboard/
│       ├── DashboardMenu.tsx         # Admin management interface
│       ├── MenuItemForm.tsx          # Create/edit form component
│       └── DeleteMenuItemDialog.tsx  # Delete confirmation dialog
└── pages/
    └── Menu.tsx                      # Public menu page with category tabs
```

## Future Enhancements

### Potential Features
1. **Image Upload**: Direct image upload functionality with file management
2. **Nutritional Information**: Calories, allergens, and detailed nutritional data
3. **Inventory Management**: Stock tracking and availability status
4. **Pricing Tiers**: Multiple pricing options (small, medium, large)
5. **Recipe Management**: Ingredient lists and preparation instructions
6. **Customer Reviews**: Rating and review system for menu items
7. **Recommendations**: AI-powered menu item suggestions
8. **Seasonal Automation**: Automatic seasonal item activation/deactivation
9. **Multi-language Support**: Internationalization for menu descriptions
10. **Nutritional Filters**: Advanced filtering by dietary restrictions

### Technical Improvements
1. **Full-Text Search**: Elasticsearch integration for menu item search
2. **Image Optimization**: Automatic image resizing and CDN integration
3. **Caching Layer**: Redis caching for frequently accessed menu data
4. **Real-time Updates**: WebSocket integration for live menu updates
5. **API Versioning**: Versioned API endpoints for backward compatibility
6. **Bulk Operations**: Bulk import/export functionality for menu management
7. **Analytics**: Menu item performance tracking and analytics
8. **A/B Testing**: Menu layout and pricing optimization

## Maintenance Guidelines

### Regular Tasks
1. **Menu Review**: Periodically review and update menu items
2. **Price Updates**: Regular price adjustments based on costs
3. **Seasonal Updates**: Update seasonal items based on availability
4. **Image Management**: Optimize and update menu item images
5. **Performance Monitoring**: Monitor API response times and database performance

### Monitoring
1. **Popular Items**: Track most viewed and ordered menu items
2. **Category Performance**: Monitor category-based engagement
3. **Search Patterns**: Analyze search and filter usage
4. **Error Tracking**: Monitor API errors and user issues

## Conclusion

The Menu System provides a comprehensive, secure, and user-friendly solution for managing menu items in the L Café application. With full CRUD operations, advanced filtering, dietary information tracking, and seamless integration with other system components, it serves as a robust foundation for restaurant menu management.

Key strengths of the implementation:
- **Comprehensive Data Model**: Supports all necessary menu item attributes
- **Security-First Design**: Complete authentication, authorization, and CSRF protection
- **Performance Optimized**: Efficient database queries, pagination, and caching strategies
- **User-Friendly Interface**: Intuitive admin dashboard and responsive public displays
- **Flexible Categorization**: Support for multiple categories and special designations
- **Dietary Information**: Comprehensive dietary restriction tracking
- **SEO-Friendly**: Automatic slug generation and sitemap integration
- **Maintainable**: Clean code structure with proper separation of concerns
- **Extensible**: Modular design allows for easy feature additions

The system successfully handles the complexity of restaurant menu management while maintaining simplicity for both administrators and customers. The comprehensive API documentation and well-structured codebase ensure smooth development and maintenance workflows.

This documentation serves as a complete reference for developers working with the menu system, providing both high-level architecture understanding and detailed implementation guidance for all aspects of menu management functionality.
```
```
